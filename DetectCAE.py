import os
import cv2
import torch
import torch.nn as nn
import tkinter as tk
from tkinter import filedialog
import numpy as np
import matplotlib.pyplot as plt
import torchvision.transforms as transforms

# Select folder for test images
root = tk.Tk()
root.withdraw()
test_folder = filedialog.askdirectory(title="Select folder of test images")

if not test_folder:
    print("No folder selected. Exiting...")
    exit()

print(f"Testing on images from: {test_folder}")

# Define Autoencoder Model (Must Match Training Script)
class Autoencoder(nn.Module):
    def __init__(self):
        super(Autoencoder, self).__init__()
        self.encoder = nn.Sequential(
            nn.Conv2d(1, 64, kernel_size=3, stride=1, padding=1),
            nn.ReLU(),
            nn.MaxPool2d(2, 2),
            nn.Conv2d(64, 128, kernel_size=3, stride=1, padding=1),
            nn.ReLU(),
            nn.MaxPool2d(2, 2),
            nn.Conv2d(128, 256, kernel_size=3, stride=1, padding=1),
            nn.ReLU(),
            nn.MaxPool2d(2, 2),
            nn.Conv2d(256, 512, kernel_size=3, stride=1, padding=1),
            nn.ReLU(),
            nn.MaxPool2d(2, 2)
        )
        self.decoder = nn.Sequential(
            nn.Conv2d(512, 256, kernel_size=3, stride=1, padding=1),
            nn.ReLU(),
            nn.Upsample(scale_factor=2),
            nn.Conv2d(256, 128, kernel_size=3, stride=1, padding=1),
            nn.ReLU(),
            nn.Upsample(scale_factor=2),
            nn.Conv2d(128, 64, kernel_size=3, stride=1, padding=1),
            nn.ReLU(),
            nn.Upsample(scale_factor=2),
            nn.Conv2d(64, 1, kernel_size=3, stride=1, padding=1),
            nn.Sigmoid(),
            nn.Upsample(scale_factor=2)
        )

    def forward(self, x):
        x = self.encoder(x)
        x = self.decoder(x)
        return x

# Load trained model
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
model = Autoencoder().to(device)
model.load_state_dict(torch.load("autoencoder_640x640.pth"))
model.eval()

# Process images one by one
for img_file in sorted(os.listdir(test_folder)):
    img_path = os.path.join(test_folder, img_file)

    # Read and preprocess image
    img = cv2.imread(img_path, cv2.IMREAD_GRAYSCALE)
    img = cv2.resize(img, (640, 640))
    img_tensor = torch.tensor(img, dtype=torch.float32).unsqueeze(0).unsqueeze(0) / 255.0
    img_tensor = img_tensor.to(device)

    # Run through autoencoder
    with torch.no_grad():
        reconstructed = model(img_tensor)

    # Compute anomaly heatmap
    error = torch.abs(img_tensor - reconstructed).cpu().numpy().squeeze()

    # Display results
    fig, ax = plt.subplots(1, 3, figsize=(15, 5))
    ax[0].imshow(img, cmap="gray")
    ax[0].set_title("Original Image")
    ax[1].imshow(reconstructed.cpu().numpy().squeeze(), cmap="gray")
    ax[1].set_title("Reconstructed Image")
    ax[2].imshow(error, cmap="hot")
    ax[2].set_title("Anomaly Heatmap")

    plt.show(block=False)  # Show image but don't block execution
    print(f"Processing: {img_file} - Press any key for next image, or 'q' to quit.")

    # Wait for user input before processing the next image
    key = input("Press Enter to continue, or 'q' to quit: ").strip().lower()
    plt.close()  # Close the figure before showing the next one

    if key == 'q':
        print("Exiting anomaly detection.")
        break

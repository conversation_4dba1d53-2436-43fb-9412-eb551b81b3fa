import os
import serial
import time
import json
import cv2
import serial.tools.list_ports
from datetime import datetime
from pypylon import pylon
import warnings
import subprocess

warnings.simplefilter('ignore', DeprecationWarning)

SAVE_IMAGES = True  # Set this flag to True to save captured images

SERIAL_PORT = "/dev/cu.usbmodem146401"
BAUD_RATE = 115200
STEP_X = 4.5
STEP_Y = 4.5
NUM_COLUMNS = 24
NUM_ROWS = 16
SPEED = 6000
ACCELERATION = 1000
START_X = 47
START_Y = 148.5
START_Z = 56 #55.3
Z_INCREMENT = 0.25
NUM_Z_IMAGES = 8  # Number of images to capture at decrementing Z positions
Z_STEP = 0.25  # Step size in the negative Z direction

HELICON_FOCUS_PATH = "/Applications/HeliconFocus.app/Contents/MacOS/HeliconFocus"  # Adjust if needed

def send_gcode(serial_port, command):
    try:
        serial_port.write((command + '\n').encode())
        responses = []
        while True:
            response = serial_port.readline().decode().strip()
            if response:
                responses.append(response)
                if "ok" in response:
                    break
        return responses
    except serial.SerialException as e:
        print(f"Error sending '{command}': {e}")
        return []

def parse_status_response(response):
    try:
        return json.loads(response)
    except json.JSONDecodeError:
        print("Error decoding JSON response")
        return None

def wait_for_idle(serial_port):
    try:
        last_status = None
        while True:
            responses = send_gcode(serial_port, "M408 S0")
            if not responses:
                time.sleep(0.2)
                continue
            for response in responses:
                if response.startswith("{") and response.endswith("}"):
                    status_data = parse_status_response(response)
                    if status_data:
                        current_status = status_data.get("status")
                        if current_status == "I":
                            print("Printer is idle.")
                            return
                        elif current_status == "B" and last_status != "B":
                            print("Printer is busy... Waiting.")
                        last_status = current_status
            time.sleep(0.2)
    except serial.SerialException as e:
        print(f"Error querying status: {e}")

def show_resized_image(window_name, frame, max_width=1920, max_height=1080):
    height, width = frame.shape[:2]
    scale = min(max_width / width, max_height / height)
    new_width = int(width * scale)
    new_height = int(height * scale)
    resized_frame = cv2.resize(frame, (new_width, new_height), interpolation=cv2.INTER_AREA)
    cv2.imshow(window_name, resized_frame)

def adjust_z_position(serial_port, camera, converter, z_start, z_increment):
    current_z = z_start
    send_gcode(serial_port, f"G1 Z{current_z:.2f} F2000")
    wait_for_idle(serial_port)

    print("Adjust Z-axis position using W and S keys. Press Enter to confirm.")

    while True:
        grab_result = camera.RetrieveResult(5000, pylon.TimeoutHandling_ThrowException)
        if grab_result.GrabSucceeded():
            image = converter.Convert(grab_result)
            frame = image.GetArray()
            show_resized_image("Live View", frame)
            key = cv2.waitKey(1)

            if key == 27:  # ESC to exit
                print("Exiting Z adjustment mode.")
                break

            elif key == 13:  # Enter key to confirm
                print(f"Z-axis position confirmed at {current_z:.2f}.")
                return current_z

            elif key == ord('w'):
                current_z += z_increment
                print(f"Moving Z up to {current_z:.2f}.")
                send_gcode(serial_port, f"G1 Z{current_z:.2f} F2000")
                wait_for_idle(serial_port)

            elif key == ord('s'):
                current_z -= z_increment
                print(f"Moving Z down to {current_z:.2f}.")
                send_gcode(serial_port, f"G1 Z{current_z:.2f} F2000")
                wait_for_idle(serial_port)

        grab_result.Release()

def focus_stack(images_dir):
    output_image = os.path.join(images_dir, "stacked_result.jpg")
    image_list_file = os.path.join(images_dir, "image_list.txt")

    try:
        image_files = sorted([
            os.path.join(images_dir, f) for f in os.listdir(images_dir)
            if f.lower().endswith(('.jpg', '.jpeg', '.png', '.tif'))
        ])

        if not image_files:
            print(f"No images found in {images_dir}. Skipping focus stacking.")
            return

        with open(image_list_file, 'w') as f:
            f.write("\n".join(image_files))

        cmd = [
            HELICON_FOCUS_PATH, "-silent",
            "-i", image_list_file,
            "-save:" + output_image,
            "-mp:1", "-rp:6", "-sp:7", "-j:100"
        ]

        print(f"Running command: {' '.join(cmd)}")

        # Run the subprocess and capture output
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print(f"Helicon Focus Output:\n{result.stdout}")

        if os.path.exists(output_image):
            print(f"Focus stacking completed. Output saved as {output_image}")
            os.remove(image_list_file)
            for img_file in image_files:
                os.remove(img_file)
            print("Deleted source images.")

            stacked_img = cv2.imread(output_image)
            if stacked_img is not None:
                show_resized_image("Stacked Image", stacked_img)
                cv2.waitKey(0)
                cv2.destroyAllWindows()
            else:
                print("Failed to load stacked image for display.")

    except subprocess.CalledProcessError as e:
        print(f"Error during focus stacking: {e.stderr}")
    except Exception as e:
        print(f"Unexpected error: {e}")


# Function to capture Z-stack and then process it using focus stacking
def capture_z_stack(serial_port, camera, converter, z_start, num_images, z_step):
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    os.makedirs(timestamp, exist_ok=True)

    for i in range(num_images):
        z_pos = z_start - (i * z_step)
        send_gcode(serial_port, f"G1 Z{z_pos:.2f} F2000")
        wait_for_idle(serial_port)

        grab_result = camera.RetrieveResult(5000, pylon.TimeoutHandling_ThrowException)

        if grab_result.GrabSucceeded():
            image = converter.Convert(grab_result)
            frame = image.GetArray()
            show_resized_image("Live View", frame)
            cv2.waitKey(1)
            image_name = f"Z_{z_pos:.2f}.jpg"
            image_path = os.path.join(timestamp, image_name)
            cv2.imwrite(image_path, frame)
            print(f"Saved: {image_path}")
            grab_result.Release()

    # Perform focus stacking in parallel using multiprocessing
    focus_stack(timestamp)

def main():
    try:
        with serial.Serial(SERIAL_PORT, BAUD_RATE, timeout=1) as ser:
            camera = pylon.InstantCamera(pylon.TlFactory.GetInstance().CreateFirstDevice())
            camera.Open()

            camera.ExposureAuto.SetValue("Off")  # Disable automatic exposure
            camera.ExposureTime.SetValue(200000.0)  # Set exposure time to 10,000 µs (adjust as needed)

            # Set image format
            camera.Width.Value = 2400
            camera.Height.Value = camera.Height.Max

            # Get the maximum width and height
            width = camera.Width.Max

            # Center the image by setting the offset
            camera.OffsetX.Value = (width - 1920) // 2

            converter = pylon.ImageFormatConverter()
            converter.OutputPixelFormat = pylon.PixelType_BGR8packed
            converter.OutputBitAlignment = pylon.OutputBitAlignment_MsbAligned

            camera.StartGrabbing(pylon.GrabStrategy_LatestImageOnly)

            send_gcode(ser, "G28")
            wait_for_idle(ser)

            send_gcode(ser, f"G1 X{START_X} Y{START_Y} F6000")
            wait_for_idle(ser)

            z_adjusted = adjust_z_position(ser, camera, converter, START_Z, Z_INCREMENT)

            capture_z_stack(ser, camera, converter, z_adjusted, NUM_Z_IMAGES, Z_STEP)

            send_gcode(ser, "G28")
            wait_for_idle(ser)

            camera.StopGrabbing()
    except serial.SerialException as e:
        print(f"Error opening serial port: {e}")


if __name__ == "__main__":
    main()

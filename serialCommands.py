import serial
import time
import json

def send_gcode(serial_port, command):
    """
    Sends a G-code command via serial and waits for an acknowledgment.
    Always returns a list, even if an error occurs.
    """
    try:
        serial_port.write((command + '\n').encode())  # Send command with newline
        print(f"Sent: {command}")  # Log sent command

        # Collect responses until "ok"
        responses = []
        while True:
            response = serial_port.readline().decode().strip()
            if response:
                # Suppress detailed JSON responses, show only "ok" or errors
                if response == "ok":
                    responses.append("ok")
                    print("Received: ok")
                    break
                elif response.startswith("{") and response.endswith("}"):
                    responses.append(response)  # Add JSON to responses if needed
                else:
                    print(f"Received: {response}")  # Log other responses (warnings, etc.)
        return responses
    except serial.SerialException as e:
        print(f"Error sending '{command}': {e}")
        return []  # Return an empty list in case of error

def parse_status_response(response):
    """
    Parses the JSON status response from the printer.
    """
    try:
        status_data = json.loads(response)
        return status_data
    except json.JSONDecodeError:
        print("Error decoding JSON response")
        return None

def wait_for_idle(serial_port):
    """
    Polls the printer status until it reports idle ("status":"I").
    Handles None or empty responses gracefully.
    """
    try:
        while True:
            responses = send_gcode(serial_port, "M408 S0")  # Query status
            if not responses:  # If no responses, skip iteration
                print("No response received; retrying...")
                time.sleep(0.2)
                continue
            for response in responses:
                if response.startswith("{") and response.endswith("}"):  # Likely the JSON response
                    status_data = parse_status_response(response)
                    if status_data and status_data.get("status") == "I":  # Check for idle status
                        print("Printer is idle.")
                        return
                    elif status_data and status_data.get("status") == "B":  # Busy status
                        print("Printer is busy... Waiting.")
            time.sleep(0.2)  # Polling interval
    except serial.SerialException as e:
        print(f"Error querying status: {e}")

def zigzag_move(serial_port, step_x, step_y, num_columns, num_rows, speed, acceleration):
    send_gcode(serial_port, f"M204 P{acceleration} T{acceleration}")

    # Move to the starting position quickly
    send_gcode(serial_port, "G1 X50 Y150 F6000")
    wait_for_idle(serial_port)

    current_y = 150
    start_x = 50
    feedrate = f"F{speed}"  # Speed in mm/min

    for row in range(num_rows):
        row_label = chr(65 + row)  # Convert row index to a letter (A, B, C, ...)
        if row % 2 == 0:
            # Even rows: move in positive X direction
            for col in range(num_columns):
                target_x = start_x + (col * step_x)
                send_gcode(serial_port, f"G1 X{target_x:.2f} Y{current_y:.2f} {feedrate}")
                wait_for_idle(serial_port)
                print(f"Current Position: {row_label}{col + 1}")
        else:
            # Odd rows: move in negative X direction
            for col in reversed(range(num_columns)):
                target_x = start_x + (col * step_x)
                send_gcode(serial_port, f"G1 X{target_x:.2f} Y{current_y:.2f} {feedrate}")
                wait_for_idle(serial_port)
                print(f"Current Position: {row_label}{col + 1}")

        current_y -= step_y  # Move down to the next row
        send_gcode(serial_port, f"G1 Y{current_y:.2f} {feedrate}")
        wait_for_idle(serial_port)

    # Home all axes after zigzag is complete
    send_gcode(serial_port, "G28")
    wait_for_idle(serial_port)

def run_macro(serial_port, macro_name):
    """
    Execute a macro by sending an M98 command via serial.
    """
    send_gcode(serial_port, f"M98 P\"/macros/{macro_name}\"")

# Serial connection parameters
SERIAL_PORT = "/dev/cu.usbmodem146401"  # Replace with your serial port
BAUD_RATE = 115200  # Typical baud rate for RepRap printers; adjust as necessary

# Parameters
STEP_X = 4.5
STEP_Y = 4.5
NUM_COLUMNS = 24
NUM_ROWS = 16
SPEED = 6000
ACCELERATION = 1000

# Open serial connection
try:
    with serial.Serial(SERIAL_PORT, BAUD_RATE, timeout=1) as ser:
        send_gcode(ser, "G28")  # Home all axes
        wait_for_idle(ser)
        zigzag_move(ser, STEP_X, STEP_Y, NUM_COLUMNS, NUM_ROWS, SPEED, ACCELERATION)
        # Uncomment these lines if you need to run additional macros
        # run_macro(ser, "zigzag.g")
        # wait_for_idle(ser)
        # run_macro(ser, "eject.g")
except serial.SerialException as e:
    print(f"Error opening serial port: {e}")

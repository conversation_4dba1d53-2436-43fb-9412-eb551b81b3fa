import tkinter as tk
from tkinter import ttk, filedialog
import re  # Import regular expressions for more robust number extraction

class MoveManager:
    def __init__(self, master):
        self.master = master
        self.master.title("G-code Move Manager")

        # Toggle for selecting axis
        self.axis_var = tk.StringVar(value="X")  # Default to X-axis
        tk.Label(master, text="Select Axis:").grid(row=0, column=0)
        tk.Radiobutton(master, text="X", variable=self.axis_var, value="X").grid(row=0, column=1)
        tk.Radiobutton(master, text="Y", variable=self.axis_var, value="Y").grid(row=0, column=2)

        # Distance entry
        self.distance_var = tk.DoubleVar()
        tk.Label(master, text="Distance (mm):").grid(row=1, column=0)
        tk.Entry(master, textvariable=self.distance_var).grid(row=1, column=1, columnspan=2)

        # Speed entry
        self.speed_var = tk.DoubleVar()
        tk.Label(master, text="Speed (mm/s):").grid(row=2, column=0)
        tk.Entry(master, textvariable=self.speed_var).grid(row=2, column=1, columnspan=2)

        # Add button
        tk.Button(master, text="Add Move", command=self.add_move).grid(row=3, column=0, columnspan=3)

        # Listbox for moves
        self.moves_list = tk.Listbox(master, height=10, width=50)
        self.moves_list.grid(row=4, column=0, columnspan=3)

        # Move Up/Down buttons
        tk.Button(master, text="Move Up", command=self.move_up).grid(row=5, column=0)
        tk.Button(master, text="Move Down", command=self.move_down).grid(row=5, column=1)

        # Export G-code button
        tk.Button(master, text="Export G-code", command=self.export_gcode).grid(row=6, column=0, columnspan=3)

    def add_move(self):
        move_details = f"{self.axis_var.get()} Move: {self.distance_var.get()}mm @ {self.speed_var.get()}mm/s"
        self.moves_list.insert(tk.END, move_details)

    def move_up(self):
        current_selection = self.moves_list.curselection()
        if current_selection:
            pos = current_selection[0]
            if pos > 0:
                self.moves_list.insert(pos-1, self.moves_list.get(pos))
                self.moves_list.delete(pos+1)
                self.moves_list.selection_set(pos-1)

    def move_down(self):
        current_selection = self.moves_list.curselection()
        if current_selection:
            pos = current_selection[0]
            if pos < self.moves_list.size() - 1:
                self.moves_list.insert(pos+2, self.moves_list.get(pos))
                self.moves_list.delete(pos)
                self.moves_list.selection_set(pos+1)

    def export_gcode(self):
        filename = filedialog.asksaveasfilename(defaultextension=".gcode",
                                                filetypes=[("G-code Files", "*.gcode"), ("All Files", "*.*")])
        if filename:
            with open(filename, "w") as file:
                for i in range(self.moves_list.size()):
                    item = self.moves_list.get(i)
                    axis, details = item.split(' Move: ')
                    distance, speed = details.split(' @ ')
                    # Extract numbers using regular expressions for more precise control
                    distance_value = re.findall(r'\d+\.?\d*', distance)[0]  # Get number only
                    speed_value = re.findall(r'\d+\.?\d*', speed)[0]        # Get number only
                    file.write(f"G1 {axis.upper()}{distance_value} F{float(speed_value) * 60}\n")  # F in mm/min

if __name__ == "__main__":
    root = tk.Tk()
    app = MoveManager(root)
    root.mainloop()

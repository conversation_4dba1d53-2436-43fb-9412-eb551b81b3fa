'''
This script is a Python program that uses OpenCV to capture images from a camera and a serial connection to control a 3D printer.
'''

import serial
import time
import json
import cv2
import serial.tools.list_ports

def find_reprap_serial_port():
    ports = list(serial.tools.list_ports.comports())
    for port in ports:
        if "RepRapFirmware" in port.description:
            print(f"Found RepRapFirmware port: {port.device}")
            return port.device
    print("Error: Could not find RepRapFirmware serial port.")
    return None


def send_gcode(serial_port, command):
    try:
        serial_port.write((command + '\n').encode())
        #print(f"Sent: {command}")

        responses = []
        while True:
            response = serial_port.readline().decode().strip()
            if response:
                #print(f"Received: {response}")
                responses.append(response)
                if "ok" in response:
                    break
        return responses
    except serial.SerialException as e:
        print(f"Error sending '{command}': {e}")
        return []


def parse_status_response(response):
    try:
        return json.loads(response)
    except json.JSONDecodeError:
        print("Error decoding JSON response")
        return None


def wait_for_idle(serial_port):
    try:
        last_status = None  # To track the last status
        while True:
            responses = send_gcode(serial_port, "M408 S0")
            if not responses:
                print("No response received; retrying...")
                time.sleep(0.2)
                continue
            for response in responses:
                if response.startswith("{") and response.endswith("}"):
                    status_data = parse_status_response(response)
                    if status_data:
                        current_status = status_data.get("status")
                        if current_status == "I":
                            print("Printer is idle.")
                            return
                        elif current_status == "B" and last_status != "B":
                            print("Printer is busy... Waiting.")
                        last_status = current_status
            time.sleep(0.2)
    except serial.SerialException as e:
        print(f"Error querying status: {e}")


def get_axis_limits(serial_port):
    """
    Queries the printer for axis limits using M208 and returns them as a dictionary.
    Stops execution if parsing fails.
    """
    responses = send_gcode(serial_port, "M208")
    axis_limits = {}

    for response in responses:
        if "Axis limits" in response:
            try:
                # Remove the "Axis limits (mm)" prefix
                response = response.replace("Axis limits (mm) ", "").strip()

                # Split into parts for each axis
                parts = response.split(", ")
                for part in parts:
                    # Each part should look like 'X0.0:230.0'
                    axis = part[0]  # First character is the axis (e.g., X, Y, Z)
                    limits = part[1:]  # Remaining is the limits (e.g., 0.0:230.0)
                    min_val, max_val = map(float, limits.split(":"))
                    axis_limits[axis] = (min_val, max_val)

            except (ValueError, IndexError) as e:
                print(f"Error parsing axis limits: {e}")
                print(f"Failed part: {part}")
                print("Stopping execution due to critical error in parsing axis limits.")
                exit(1)

    # Ensure all axis limits are parsed
    if not all(axis in axis_limits for axis in ["X", "Y", "Z"]):
        print("Error: Missing axis limits after parsing.")
        exit(1)

    return axis_limits


def continuous_autofocus(serial_port, camera, z_start, z_end, z_feedrate=500):
    print("Starting continuous autofocus sequence...")

    send_gcode(serial_port, f"G1 Z{z_start} F1000")
    wait_for_idle(serial_port)

    start_time = time.time()
    send_gcode(serial_port, f"G1 Z{z_end} F{z_feedrate}")
    print(f"Moving Z-axis from {z_start} to {z_end} at feedrate {z_feedrate}")

    best_sharpness = 0
    best_z = z_start
    current_z = z_start

    motion_duration = abs(z_start - z_end) / (z_feedrate / 60)

    while time.time() - start_time < motion_duration:
        ret, frame = camera.read()
        if not ret:
            print("Failed to capture image during autofocus.")
            continue

        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        sharpness = cv2.Laplacian(gray, cv2.CV_64F).var()
        print(f"Z: {current_z}, Sharpness: {sharpness}")

        if sharpness > best_sharpness:
            best_sharpness = sharpness
            best_z = current_z

        elapsed_time = time.time() - start_time
        current_z = z_start + (z_feedrate / 60) * elapsed_time if z_start < z_end else z_start - (z_feedrate / 60) * elapsed_time

    send_gcode(serial_port, f"G1 Z{best_z} F500")
    wait_for_idle(serial_port)
    print(f"Continuous autofocus complete. Best Z position: {best_z}")
    return best_z


def fine_tune_autofocus(serial_port, camera, coarse_z, z_range=5, z_step=0.1, z_feedrate=100):
    """
    Perform fine-tune autofocus around the coarse Z position.

    :param serial_port: The serial connection to the 3D printer.
    :param camera: The camera object to capture images.
    :param coarse_z: The coarse Z position to start fine-tuning from.
    :param z_range: The range in mm to sweep above and below the coarse position.
    :param z_step: The step size in mm for fine-tuning.
    :param z_feedrate: The feedrate for Z-axis movement during fine-tuning.
    :return: The best Z position for sharp focus.
    """
    print("Starting fine-tune autofocus sequence...")

    # Define the fine-tuning range
    fine_start = coarse_z - z_range / 2
    fine_end = coarse_z + z_range / 2

    best_sharpness = 0
    best_z = coarse_z

    current_z = fine_start
    while current_z <= fine_end:
        # Move to the current Z position
        send_gcode(serial_port, f"G1 Z{current_z:.2f} F{z_feedrate}")
        wait_for_idle(serial_port)

        # Capture an image and calculate its sharpness
        ret, frame = camera.read()
        if not ret:
            print("Failed to capture image during fine-tune autofocus.")
            current_z += z_step
            continue

        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        sharpness = cv2.Laplacian(gray, cv2.CV_64F).var()
        print(f"Z: {current_z:.2f}, Sharpness: {sharpness:.2f}")

        # Update the best sharpness and Z position if the current one is better
        if sharpness > best_sharpness:
            best_sharpness = sharpness
            best_z = current_z

        # Increment the Z position by the step size
        current_z += z_step

    # Move to the best Z position
    send_gcode(serial_port, f"G1 Z{best_z:.2f} F{z_feedrate}")
    wait_for_idle(serial_port)

    print(f"Fine-tune autofocus complete. Best Z position: {best_z:.2f}")
    return best_z


def zigzag_move(serial_port, step_x, step_y, num_columns, num_rows, speed, acceleration, camera):
    send_gcode(serial_port, f"M204 P{acceleration} T{acceleration}")
    send_gcode(serial_port, "G1 X50 Y150 F6000")
    wait_for_idle(serial_port)

    current_y = 150
    start_x = 50
    feedrate = f"F{speed}"

    for row in range(num_rows):
        row_label = chr(65 + row)
        if row % 2 == 0:
            for col in range(num_columns):
                target_x = start_x + (col * step_x)
                send_gcode(serial_port, f"G1 X{target_x:.2f} Y{current_y:.2f} {feedrate}")
                wait_for_idle(serial_port)
                time.sleep(0.2)  # Pause briefly before taking the image
                ret, frame = camera.read()
                if ret:
                    image_name = f"{row_label}{col + 1}.jpg"
                    cv2.imwrite(image_name, frame)
                    print(f"Saved image: {image_name}")
        else:
            for col in reversed(range(num_columns)):
                target_x = start_x + (col * step_x)
                send_gcode(serial_port, f"G1 X{target_x:.2f} Y{current_y:.2f} {feedrate}")
                wait_for_idle(serial_port)
                time.sleep(0.2)  # Pause briefly before taking the image
                ret, frame = camera.read()
                if ret:
                    image_name = f"{row_label}{col + 1}.jpg"
                    cv2.imwrite(image_name, frame)
                    print(f"Saved image: {image_name}")

        current_y -= step_y
        send_gcode(serial_port, f"G1 Y{current_y:.2f} {feedrate}")
        wait_for_idle(serial_port)


SERIAL_PORT = find_reprap_serial_port()
if SERIAL_PORT is None:
    print("No valid serial port found. Exiting.")
    exit(1)

BAUD_RATE = 115200
STEP_X = 4.5
STEP_Y = 4.5
NUM_COLUMNS = 24
NUM_ROWS = 16
SPEED = 6000
ACCELERATION = 1000

try:
    with serial.Serial(SERIAL_PORT, BAUD_RATE, timeout=1) as ser:
        camera = cv2.VideoCapture(1)
        if not camera.isOpened():
            print("Error: Could not open the camera.")
        else:
            send_gcode(ser, "G28")
            wait_for_idle(ser)

            axis_limits = get_axis_limits(ser)
            print(f"Axis limits: {axis_limits}")
            z_end, z_start = axis_limits["Z"]
            z_start = z_start - 150

            send_gcode(ser, f"G1 Z{z_start} F6000")
            send_gcode(ser, f"G1 X50 Y150 F6000")
            wait_for_idle(ser)

            best_z = continuous_autofocus(ser, camera, z_start, z_end)
            send_gcode(ser, f"G1 Z{best_z} F500")
            wait_for_idle(ser)

            fine_tuned_z = fine_tune_autofocus(ser, camera, best_z, z_range=5, z_step=0.1, z_feedrate=100)
            send_gcode(ser, f"G1 Z{fine_tuned_z} F500")
            wait_for_idle(ser)

            zigzag_move(ser, STEP_X, STEP_Y, NUM_COLUMNS, NUM_ROWS, SPEED, ACCELERATION, camera)

            send_gcode(ser, "G28")
            wait_for_idle(ser)

        camera.release()
except serial.SerialException as e:
    print(f"Error opening serial port: {e}")

import os
import serial
import time
import json
import cv2
import numpy as np
import serial.tools.list_ports
from datetime import datetime
from pypylon import pylon
import warnings

warnings.simplefilter('ignore', DeprecationWarning)

# Configuration Parameters
SERIAL_PORT = "/dev/cu.usbmodem146401"
BAUD_RATE = 115200
START_X = 46.6
START_Y = 148.1
START_Z = 59
Z_INCREMENT = 0.1
NUM_Z_IMAGES = 100
Z_STEP = 0.1
SHARPNESS_THRESHOLD = 1000  # Minimum sharpness required to replace pixels

# Global variables for focus stacking
sharpness_map = None
stacked_image = None


def send_gcode(serial_port, command):
    try:
        serial_port.write((command + '\n').encode())
        responses = []
        while True:
            response = serial_port.readline().decode().strip()
            if response:
                responses.append(response)
                if "ok" in response:
                    break
        return responses
    except serial.SerialException as e:
        print(f"Error sending '{command}': {e}")
        return []


def parse_status_response(response):
    try:
        return json.loads(response)
    except json.JSONDecodeError:
        print("Error decoding JSON response")
        return None


def wait_for_idle(serial_port):
    try:
        last_status = None
        while True:
            responses = send_gcode(serial_port, "M408 S0")
            if not responses:
                time.sleep(0.2)
                continue
            for response in responses:
                if response.startswith("{") and response.endswith("}"):
                    status_data = parse_status_response(response)
                    if status_data:
                        current_status = status_data.get("status")
                        if current_status == "I":
                            print("Printer is idle.")
                            return
                        elif current_status == "B" and last_status != "B":
                            print("Printer is busy... Waiting.")
                        last_status = current_status
            time.sleep(0.2)
    except serial.SerialException as e:
        print(f"Error querying status: {e}")


def update_focus_stacking(image, initialize=False):
    """
    Updates the focus-stacked image in real-time as new images arrive.

    :param image: Newly captured frame from the camera.
    :param initialize: If True, initialize stacked_image with the first image.
    """
    global sharpness_map, stacked_image

    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

    # Compute Tenengrad sharpness using Sobel filters
    sobel_x = cv2.Sobel(gray, cv2.CV_64F, 1, 0, ksize=3)
    sobel_y = cv2.Sobel(gray, cv2.CV_64F, 0, 1, ksize=3)
    tenengrad = np.square(sobel_x) + np.square(sobel_y)

    if initialize:
        # Use the first image as the base for stacking
        stacked_image = image.copy()
        sharpness_map = tenengrad.copy()
        return stacked_image

    # Find pixels where the new image is sharper than the previously recorded sharpness
    mask = (tenengrad > sharpness_map) & (tenengrad > SHARPNESS_THRESHOLD)

    # Update sharpness map and stacked image only where sharpness increases
    sharpness_map[mask] = tenengrad[mask]
    stacked_image[mask] = image[mask]

    return stacked_image  # Return updated image for optional real-time display


def capture_z_stack_on_the_fly(ser, camera, converter, start_z, num_images, z_step):
    """
    Captures a Z-stack and updates the focus-stacked image dynamically.
    """
    global stacked_image

    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    output_folder = f"focus_stack_{timestamp}"
    os.makedirs(output_folder, exist_ok=True)

    for i in range(num_images):
        z_pos = start_z - i * z_step
        send_gcode(ser, f"G1 Z{z_pos} F600")
        print(f"Current Z position: {z_pos}")
        wait_for_idle(ser)

        grab_result = camera.RetrieveResult(5000, pylon.TimeoutHandling_ThrowException)
        if grab_result.GrabSucceeded():
            img = converter.Convert(grab_result)
            image = img.GetArray()

            # Save the original Z-stack image
            img_path = os.path.join(output_folder, f"z_{i:03d}.jpg")
            cv2.imwrite(img_path, image)

            # Process the first image as the base image
            if i == 0:
                stacked_image = update_focus_stacking(image, initialize=True)
            else:
                stacked_image = update_focus_stacking(image)

            # Optional: Display real-time stacking progress
            cv2.imshow("Live Focus Stacking", stacked_image)
            cv2.waitKey(1)

        grab_result.Release()

    # Save the final stacked image
    result_path = os.path.join(output_folder, "focus_stacked_live.jpg")
    cv2.imwrite(result_path, stacked_image)
    print(f"Real-time focus stacked image saved at: {result_path}")

    cv2.destroyAllWindows()



def main():
    try:
        with serial.Serial(SERIAL_PORT, BAUD_RATE, timeout=1) as ser:
            camera = pylon.InstantCamera(pylon.TlFactory.GetInstance().CreateFirstDevice())
            camera.Open()

            camera.ExposureAuto.SetValue("Off")
            camera.ExposureTime.SetValue(200000.0)

            # Set image format
            camera.Width.Value = 1920
            camera.Height.Value = camera.Height.Max

            # Get the maximum width and height
            width = camera.Width.Max

            # Center the image by setting the offset
            camera.OffsetX.Value = (width - 1920) // 2

            converter = pylon.ImageFormatConverter()
            converter.OutputPixelFormat = pylon.PixelType_BGR8packed
            converter.OutputBitAlignment = pylon.OutputBitAlignment_MsbAligned

            camera.StartGrabbing(pylon.GrabStrategy_LatestImageOnly)

            send_gcode(ser, "G28")
            wait_for_idle(ser)

            send_gcode(ser, f"G1 X{START_X} Y{START_Y} F6000")
            wait_for_idle(ser)

            capture_z_stack_on_the_fly(ser, camera, converter, START_Z, NUM_Z_IMAGES, Z_STEP)

            send_gcode(ser, "G28")
            wait_for_idle(ser)

            camera.StopGrabbing()
    except serial.SerialException as e:
        print(f"Error opening serial port: {e}")


if __name__ == "__main__":
    main()

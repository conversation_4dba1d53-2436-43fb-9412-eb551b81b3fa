from fastai.vision.all import *
from tkinter import Tk, filedialog
import time

# 1. Load the trained model
model_path = "good_bad_classifier_efficientnet.pkl"
learn = load_learner(model_path)
print("Model loaded successfully!")


# 2. Select an image using file explorer
def select_image():
    Tk().withdraw()  # Hide the main Tkinter window
    file_path = filedialog.askopenfilename(
        title="Select an Image",
        filetypes=[("Image Files", "*.png;*.jpg;*.jpeg")]
    )
    return file_path


# 3. Predict the class of the selected image and measure duration
def test_model():
    img_path = select_image()
    if img_path:
        print(f"Selected image: {img_path}")
        durations = []

        for i in range(5):  # Run inference 5 times
            img = PILImage.create(img_path)

            start_time = time.time()
            pred_class, pred_idx, probs = learn.predict(img)
            end_time = time.time()

            duration = end_time - start_time
            durations.append(duration)

            print(f"Run {i + 1}: Predicted Class: {pred_class}, "
                  f"Probability: {probs[pred_idx]:.4f}, "
                  f"Duration: {duration:.4f} seconds")

        print(f"\nAverage Duration: {sum(durations) / len(durations):.4f} seconds")
    else:
        print("No image selected!")


# Run the test function
if __name__ == "__main__":
    test_model()

import os
import serial
import time
import json
import cv2
import numpy as np
import serial.tools.list_ports
from datetime import datetime
from pypylon import pylon
import warnings

warnings.simplefilter('ignore', DeprecationWarning)

SAVE_IMAGES = True  # Set this flag to True to save captured images

SERIAL_PORT = "/dev/cu.usbmodem146401"
BAUD_RATE = 115200
START_X = 47
START_Y = 148.5
START_Z = 59
NUM_Z_IMAGES = 25
Z_STEP = 0.1

def send_gcode(serial_port, command):
    try:
        serial_port.write((command + '\n').encode())
        responses = []
        while True:
            response = serial_port.readline().decode().strip()
            if response:
                responses.append(response)
                if "ok" in response:
                    break
        return responses
    except serial.SerialException as e:
        print(f"Error sending '{command}': {e}")
        return []

def wait_for_idle(serial_port):
    try:
        last_status = None
        while True:
            responses = send_gcode(serial_port, "M408 S0")
            if not responses:
                time.sleep(0.2)
                continue
            for response in responses:
                if response.startswith("{") and response.endswith("}"):
                    status_data = json.loads(response)
                    if status_data.get("status") == "I":
                        print("Printer is idle.")
                        return
                    elif status_data.get("status") == "B" and last_status != "B":
                        print("Printer is busy... Waiting.")
                    last_status = status_data.get("status")
            time.sleep(0.2)
    except serial.SerialException as e:
        print(f"Error querying status: {e}")

def show_resized_image(window_name, frame, max_width=1920, max_height=1080):
    height, width = frame.shape[:2]
    scale = min(max_width / width, max_height / height)
    new_size = (int(width * scale), int(height * scale))
    resized_frame = cv2.resize(frame, new_size, interpolation=cv2.INTER_AREA)
    cv2.imshow(window_name, resized_frame)
    cv2.waitKey(1)

def detect_circle(image, expected_diameter=1200, tolerance=10):
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    blurred = cv2.GaussianBlur(gray, (9, 9), 2)
    circles = cv2.HoughCircles(
        blurred, cv2.HOUGH_GRADIENT, dp=1.2, minDist=50,
        param1=50, param2=30,
        minRadius=int((expected_diameter - tolerance) / 2),
        maxRadius=int((expected_diameter + tolerance) / 2)
    )
    if circles is not None:
        # Draw the detected circle
        circles = np.uint16(np.around(circles))
        for i in circles[0, :]:
            cv2.circle(image, (i[0], i[1]), i[2], (0, 255, 0), 2)
            cv2.circle(image, (i[0], i[1]), 2, (0, 0, 255), 3)
        show_resized_image("Detected Circle", image)
        return np.uint16(np.around(circles))[0, 0]  # Return only the first detected circle
    return None

def compute_sharpness(image, circle):
    x, y, r = circle
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    x1, y1, x2, y2 = max(0, x - r), max(0, y - r), min(gray.shape[1], x + r), min(gray.shape[0], y + r)
    roi = gray[y1:y2, x1:x2]
    sobel_x = cv2.Sobel(roi, cv2.CV_64F, 1, 0, ksize=3)
    sobel_y = cv2.Sobel(roi, cv2.CV_64F, 0, 1, ksize=3)
    return np.mean(sobel_x**2 + sobel_y**2)

def find_best_focus(serial_port, camera, converter, z_start, z_step, max_steps):
    best_focus_z, max_sharpness = None, 0
    for i in range(max_steps):
        z_pos = z_start - (i * z_step)
        send_gcode(serial_port, f"G1 Z{z_pos:.2f} F2000")
        wait_for_idle(serial_port)
        grab_result = camera.RetrieveResult(5000, pylon.TimeoutHandling_ThrowException)
        if grab_result.GrabSucceeded():
            image = converter.Convert(grab_result).GetArray()
            circle = detect_circle(image)
            if circle is not None:
                sharpness = compute_sharpness(image, circle)
                if sharpness > max_sharpness:
                    max_sharpness, best_focus_z = sharpness, z_pos
            show_resized_image("Live View", image)
            grab_result.Release()
    if best_focus_z is not None:
        send_gcode(serial_port, f"G1 Z{best_focus_z:.2f} F2000")
        wait_for_idle(serial_port)
    return best_focus_z

def main():
    with serial.Serial(SERIAL_PORT, BAUD_RATE, timeout=1) as ser:
        camera = pylon.InstantCamera(pylon.TlFactory.GetInstance().CreateFirstDevice())
        camera.Open()
        camera.ExposureAuto.SetValue("Off")
        camera.ExposureTime.SetValue(200000.0)
        # Set image format
        camera.Width.Value = 1920
        camera.Height.Value = camera.Height.Max
        # Get the maximum width
        width = camera.Width.Max
        # Center the image by setting the offset
        camera.OffsetX.Value = (width - 1920) // 2
        converter = pylon.ImageFormatConverter()
        converter.OutputPixelFormat, converter.OutputBitAlignment = pylon.PixelType_BGR8packed, pylon.OutputBitAlignment_MsbAligned
        camera.StartGrabbing(pylon.GrabStrategy_LatestImageOnly)
        send_gcode(ser, "G28")
        wait_for_idle(ser)
        send_gcode(ser, f"G1 X{START_X} Y{START_Y} F6000")
        wait_for_idle(ser)
        best_focus_z = find_best_focus(ser, camera, converter, START_Z, Z_STEP, NUM_Z_IMAGES)
        if best_focus_z is not None:
            print(f"Best focus at Z={best_focus_z:.2f}")
        # Move to the best focus position
        send_gcode(ser, f"G1 Z{best_focus_z:.2f} F2000")
        wait_for_idle(ser)
        # Show the final image
        grab_result = camera.RetrieveResult(5000, pylon.TimeoutHandling_ThrowException)
        if grab_result.GrabSucceeded():
            image = converter.Convert(grab_result).GetArray()
            show_resized_image("Best Focus Image", image)
            grab_result.Release()
        # Wait for user to close the window
        cv2.waitKey(0)
        camera.StopGrabbing()
        cv2.destroyAllWindows()

if __name__ == "__main__":
    main()

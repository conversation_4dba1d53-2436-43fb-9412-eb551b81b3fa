if __name__ == "__main__":
    from openvino.runtime import Core
    import numpy as np

    # Load OpenVINO model
    ie = Core()
    model = ie.read_model(model="openvino_model/yolov11.xml")
    compiled_model = ie.compile_model(model=model, device_name="CPU")

    # Generate a random input tensor
    input_tensor = np.random.randn(1, 3, 640, 640).astype(np.float32)

    # Run inference
    output = compiled_model([input_tensor])

    print("✅ OpenVINO inference completed successfully!")



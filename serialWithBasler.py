'''
This script demonstrates how to use a <PERSON><PERSON> camera to perform autofocus and image capture
'''

import serial
import time
import json
import cv2
import numpy as np
import serial.tools.list_ports
from pypylon import pylon
import warnings

warnings.simplefilter('ignore', DeprecationWarning)

def find_reprap_serial_port():
    ports = list(serial.tools.list_ports.comports())
    for port in ports:
        if "RepRapFirmware" in port.description:
            print(f"Found RepRapFirmware port: {port.device}")
            return port.device
    print("Error: Could not find RepRapFirmware serial port.")
    return None

def send_gcode(serial_port, command):
    try:
        serial_port.write((command + '\n').encode())
        responses = []
        while True:
            response = serial_port.readline().decode().strip()
            if response:
                responses.append(response)
                if "ok" in response:
                    break
        return responses
    except serial.SerialException as e:
        print(f"Error sending '{command}': {e}")
        return []

def parse_status_response(response):
    try:
        return json.loads(response)
    except json.JSONDecodeError:
        print("Error decoding JSON response")
        return None

def wait_for_idle(serial_port):
    try:
        last_status = None
        while True:
            responses = send_gcode(serial_port, "M408 S0")
            if not responses:
                time.sleep(0.2)
                continue
            for response in responses:
                if response.startswith("{") and response.endswith("}"):
                    status_data = parse_status_response(response)
                    if status_data:
                        current_status = status_data.get("status")
                        if current_status == "I":
                            print("Printer is idle.")
                            return
                        elif current_status == "B" and last_status != "B":
                            print("Printer is busy... Waiting.")
                        last_status = current_status
            time.sleep(0.2)
    except serial.SerialException as e:
        print(f"Error querying status: {e}")

def get_axis_limits(serial_port):
    """
    Queries the printer for axis limits using M208 and returns them as a dictionary.
    Stops execution if parsing fails.
    """
    responses = send_gcode(serial_port, "M208")
    axis_limits = {}

    for response in responses:
        if "Axis limits" in response:
            try:
                # Remove the "Axis limits (mm)" prefix
                response = response.replace("Axis limits (mm) ", "").strip()

                # Split into parts for each axis
                parts = response.split(", ")
                for part in parts:
                    # Each part should look like 'X0.0:230.0'
                    axis = part[0]  # First character is the axis (e.g., X, Y, Z)
                    limits = part[1:]  # Remaining is the limits (e.g., 0.0:230.0)
                    min_val, max_val = map(float, limits.split(":"))
                    axis_limits[axis] = (min_val, max_val)

            except (ValueError, IndexError) as e:
                print(f"Error parsing axis limits: {e}")
                print(f"Failed part: {part}")
                print("Stopping execution due to critical error in parsing axis limits.")
                exit(1)

    # Ensure all axis limits are parsed
    if not all(axis in axis_limits for axis in ["X", "Y", "Z"]):
        print("Error: Missing axis limits after parsing.")
        exit(1)

    return axis_limits

def calculate_tenengrad(image):
    """
    Calculate sharpness using the Tenengrad method (gradient magnitude).
    """
    # Convert the image to grayscale
    gray_image = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

    # Calculate gradients using Sobel operators
    sobel_x = cv2.Sobel(gray_image, cv2.CV_64F, 1, 0, ksize=3)
    sobel_y = cv2.Sobel(gray_image, cv2.CV_64F, 0, 1, ksize=3)

    # Compute the gradient magnitude
    gradient_magnitude = np.sqrt(sobel_x ** 2 + sobel_y ** 2)

    # Compute the mean gradient magnitude as the sharpness score
    sharpness = np.mean(gradient_magnitude)
    return sharpness

def coarse_autofocus(serial_port, camera, converter, z_start, z_end, z_feedrate=500):
    print("Starting continuous autofocus sequence...")

    send_gcode(serial_port, f"G1 Z{z_start} F2000")
    wait_for_idle(serial_port)

    start_time = time.time()
    send_gcode(serial_port, f"G1 Z{z_end} F{z_feedrate}")
    print(f"Moving Z-axis from {z_start} to {z_end} at feedrate {z_feedrate}")

    best_sharpness = 0
    best_z = z_start
    current_z = z_start

    motion_duration = abs(z_start - z_end) / (z_feedrate / 60)

    while time.time() - start_time < motion_duration:
        grab_result = camera.RetrieveResult(5000, pylon.TimeoutHandling_ThrowException)
        if grab_result.GrabSucceeded():
            image = converter.Convert(grab_result)
            frame = image.GetArray()
            cv2.imshow("Live View", frame)  # Show color image live
            cv2.waitKey(1)  # Necessary to refresh the image display
            sharpness = calculate_tenengrad(frame)
            print(f"Z: {current_z}, Sharpness: {sharpness}")

            if sharpness > best_sharpness:
                best_sharpness = sharpness
                best_z = current_z

        grab_result.Release()
        elapsed_time = time.time() - start_time
        current_z = z_start + (z_feedrate / 60) * elapsed_time if z_start < z_end else z_start - (z_feedrate / 60) * elapsed_time

    send_gcode(serial_port, f"G1 Z{best_z} F2000")
    wait_for_idle(serial_port)
    print(f"Continuous autofocus complete. Best Z position: {best_z}")
    return best_z

def fine_tune_autofocus(serial_port, camera, converter, coarse_z, z_range=5, z_step=0.1, z_feedrate=100, stop_threshold=5, rise_threshold=5):
    print("Starting fine-tune autofocus sequence...")

    fine_start = coarse_z - z_range / 2
    fine_end = coarse_z + z_range / 2

    best_sharpness = 0
    best_z = coarse_z

    current_z = fine_start
    drop_count = 0
    rise_count = 0
    last_sharpness = 0

    while current_z <= fine_end:
        send_gcode(serial_port, f"G1 Z{current_z:.2f} F{z_feedrate}")
        wait_for_idle(serial_port)

        grab_result = camera.RetrieveResult(5000, pylon.TimeoutHandling_ThrowException)
        if grab_result.GrabSucceeded():
            image = converter.Convert(grab_result)
            frame = image.GetArray()
            cv2.imshow("Live View", frame)  # Show color image live
            cv2.waitKey(1)  # Necessary to refresh the image display
            sharpness = calculate_tenengrad(frame)
            print(f"Z: {current_z:.2f}, Sharpness: {sharpness:.2f}")

            if sharpness > last_sharpness:
                rise_count += 1
                drop_count = 0  # Reset drop count since sharpness improved
                if rise_count >= rise_threshold:
                    best_sharpness = sharpness
                    best_z = current_z
            elif sharpness < last_sharpness:
                drop_count += 1
                if drop_count >= stop_threshold and rise_count >= rise_threshold:
                    print("Sharpness peaked and started dropping after sufficient rise. Stopping fine-tune scan early.")
                    break

            last_sharpness = sharpness

        grab_result.Release()
        current_z += z_step

    send_gcode(serial_port, f"G1 Z{best_z:.2f} F{z_feedrate}")
    wait_for_idle(serial_port)

    print(f"Fine-tune autofocus complete. Best Z position: {best_z:.2f}")
    return best_z

def zigzag_move(serial_port, camera, converter, step_x, step_y, num_columns, num_rows, speed, acceleration):
    send_gcode(serial_port, f"M204 P{acceleration} T{acceleration}")
    send_gcode(serial_port, "G1 X50 Y150 F6000")
    wait_for_idle(serial_port)

    current_y = 150
    start_x = 50
    feedrate = f"F{speed}"

    for row in range(num_rows):
        row_label = chr(65 + row)
        if row % 2 == 0:
            for col in range(num_columns):
                target_x = start_x + (col * step_x)
                send_gcode(serial_port, f"G1 X{target_x:.2f} Y{current_y:.2f} {feedrate}")
                wait_for_idle(serial_port)
                time.sleep(0.2)
                grab_result = camera.RetrieveResult(5000, pylon.TimeoutHandling_ThrowException)
                if grab_result.GrabSucceeded():
                    image = converter.Convert(grab_result)
                    frame = image.GetArray()
                    cv2.imshow("Live View", frame)  # Show color image live
                    cv2.waitKey(1)  # Necessary to refresh the image display
                    image_name = f"{row_label}{col + 1}.jpg"
                    #cv2.imwrite(image_name, frame)
                    print(f"Position: {image_name}")
                grab_result.Release()
        else:
            for col in reversed(range(num_columns)):
                target_x = start_x + (col * step_x)
                send_gcode(serial_port, f"G1 X{target_x:.2f} Y{current_y:.2f} {feedrate}")
                wait_for_idle(serial_port)
                time.sleep(0.2)
                grab_result = camera.RetrieveResult(5000, pylon.TimeoutHandling_ThrowException)
                if grab_result.GrabSucceeded():
                    image = converter.Convert(grab_result)
                    frame = image.GetArray()
                    cv2.imshow("Live View", frame)  # Show color image live
                    cv2.waitKey(1)  # Necessary to refresh the image display
                    image_name = f"{row_label}{col + 1}.jpg"
                    #cv2.imwrite(image_name, frame)
                    print(f"Position: {image_name}")
                grab_result.Release()

        current_y -= step_y
        send_gcode(serial_port, f"G1 Y{current_y:.2f} {feedrate}")
        wait_for_idle(serial_port)

SERIAL_PORT = find_reprap_serial_port()
if SERIAL_PORT is None:
    print("No valid serial port found. Exiting.")
    exit(1)

BAUD_RATE = 115200
STEP_X = 4.5
STEP_Y = 4.5
NUM_COLUMNS = 24
NUM_ROWS = 16
SPEED = 6000
ACCELERATION = 1000

try:
    with serial.Serial(SERIAL_PORT, BAUD_RATE, timeout=1) as ser:
        # Initialize Basler camera
        # Initialize Basler camera
        camera = pylon.InstantCamera(pylon.TlFactory.GetInstance().CreateFirstDevice())
        camera.Open()  # Open the camera to configure settings

        # Set camera parameters
        camera.AcquisitionFrameRateEnable.SetValue(True)  # Enable manual frame rate control
        camera.AcquisitionFrameRate.SetValue(30.0)  # Set frame rate to 30 fps

        camera.ExposureAuto.SetValue("Off")  # Disable automatic exposure
        camera.ExposureTime.SetValue(10000.0)  # Set exposure time to 10,000 µs (adjust as needed)

        camera.GainAuto.SetValue("Off")  # Disable automatic gain
        camera.Gain.SetValue(10.0)  # Set gain to 10.0 (adjust as needed)

        # If desired, enable white balance auto adjustment
        # camera.BalanceWhiteAuto.SetValue("Continuous")

        # Image Format Converter settings
        converter = pylon.ImageFormatConverter()
        converter.OutputPixelFormat = pylon.PixelType_BGR8packed
        converter.OutputBitAlignment = pylon.OutputBitAlignment_MsbAligned

        # Start grabbing images after setting parameters
        camera.StartGrabbing(pylon.GrabStrategy_LatestImageOnly)

        send_gcode(ser, "G28") # Home all axes
        wait_for_idle(ser) # Wait until the printer is idle

        axis_limits = get_axis_limits(ser) # Get axis limits
        print(f"Axis limits: {axis_limits}")
        z_end, z_start = axis_limits["Z"] # Get Z-axis limit
        z_start = z_start - 150

        send_gcode(ser, f"G1 Z{z_start} F2000") # Move to the starting Z position
        send_gcode(ser, f"G1 X50 Y150 F6000") # Move to the starting XY position
        wait_for_idle(ser)

        best_z = coarse_autofocus(ser, camera, converter, z_start, z_end) # Perform coarse autofocus
        send_gcode(ser, f"G1 Z{best_z} F2000") # Move to the best Z position
        wait_for_idle(ser)

        fine_tuned_z = fine_tune_autofocus(ser, camera, converter, best_z, z_range=10, z_step=0.1, z_feedrate=6000)
        send_gcode(ser, f"G1 Z{fine_tuned_z} F2000")
        wait_for_idle(ser)

        zigzag_move(ser, camera, converter, STEP_X, STEP_Y, NUM_COLUMNS, NUM_ROWS, SPEED, ACCELERATION)

        send_gcode(ser, "G28") # Home all axes
        wait_for_idle(ser)

        camera.StopGrabbing()
except serial.SerialException as e:
    print(f"Error opening serial port: {e}")

from pypylon import pylon
import cv2
import warnings
import numpy as np
from collections import deque

warnings.simplefilter('ignore', DeprecationWarning)


def moving_average_filter(values, new_value, max_size=10000):
    """Maintain a moving average filter."""
    values.append(new_value)
    if len(values) > max_size:
        values.popleft()
    return sum(values) / len(values)


def calculate_enclosing_circle_ratio(contour):
    """Compute ratio of contour area to enclosing circle area."""
    area = cv2.contourArea(contour)
    (x, y), radius = cv2.minEnclosingCircle(contour)
    enclosing_circle_area = np.pi * (radius ** 2)
    if enclosing_circle_area == 0:
        return 0
    return area / enclosing_circle_area


def get_sector_ratios(contour, circle_center, radius, sector_area_buffers):
    """
    Divide the enclosing circle into 30-degree sectors and calculate
    the proportion of the contour area in each sector.
    """
    if radius == 0:
        return np.zeros(12)  # Return all zeros if radius is zero

    sector_ratios = np.zeros(12)  # 12 sectors for 30-degree divisions
    sector_areas = np.zeros(12)  # To count actual contour area in each sector
    cx, cy = circle_center

    # Compute contour points in polar coordinates
    for point in contour:
        px, py = point[0]  # Extract coordinates
        dx, dy = px - cx, py - cy  # Compute displacement from center
        angle = np.arctan2(dy, dx) * 180 / np.pi  # Convert to degrees
        if angle < 0:
            angle += 360  # Convert to range [0, 360)

        sector_idx = int(angle // 30)  # Find sector index (0-11)
        sector_areas[sector_idx] += 1  # Count points in the sector

    # Smooth the sector areas using moving average filtering
    smoothed_sector_areas = np.zeros(12)
    for i in range(12):
        smoothed_sector_areas[i] = moving_average_filter(sector_area_buffers[i], sector_areas[i])

    total_contour_area = cv2.contourArea(contour)
    if total_contour_area == 0:
        return np.zeros(12)  # Prevent division by zero

    # Compute max possible area for each sector (1/12 of the enclosing circle)
    sector_max_area = (np.pi * radius ** 2) / 12

    # Normalize sector ratios based on smoothed sector areas
    for i in range(12):
        sector_ratios[i] = min((smoothed_sector_areas[i] / sum(smoothed_sector_areas)) *
                               (total_contour_area / sector_max_area) * 100, 100)

    return sector_ratios


def draw_text_info(img, circle_fit_ratio, smoothed_sector_ratios):
    """
    Display the Circle Fit ratio and smoothed sector ratios on the **left** side of the image.
    """
    start_x = 10  # Left side
    start_y = 30
    line_spacing = 30

    # Display "Circle Fit" at the top
    cv2.putText(img, f"Circle Fit: {int(circle_fit_ratio * 100)}%",
                (start_x, start_y), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)

    # Display each sector ratio below it
    for i, ratio in enumerate(smoothed_sector_ratios):
        text = f"Sector {i * 30}-{(i + 1) * 30}: {int(ratio)}%"
        cv2.putText(img, text, (start_x, start_y + (i + 1) * line_spacing),
                    cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 1)


def main():
    camera = pylon.InstantCamera(pylon.TlFactory.GetInstance().CreateFirstDevice())
    camera.StartGrabbing(pylon.GrabStrategy_LatestImageOnly)
    converter = pylon.ImageFormatConverter()
    converter.OutputPixelFormat = pylon.PixelType_BGR8packed
    converter.OutputBitAlignment = pylon.OutputBitAlignment_MsbAligned

    enclosing_circle_ratio_values = deque(maxlen=10)
    sector_ratio_values = [deque(maxlen=10) for _ in range(12)]  # 12 smoothing buffers for sector ratios
    sector_area_buffers = [deque(maxlen=10) for _ in range(12)]  # 12 smoothing buffers for sector areas

    while camera.IsGrabbing():
        grab_result = camera.RetrieveResult(5000, pylon.TimeoutHandling_ThrowException)
        key = cv2.waitKey(1) & 0xFF
        if key == 27:
            break

        if grab_result.GrabSucceeded():
            image = converter.Convert(grab_result)
            img = image.GetArray()
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
            _, thresh = cv2.threshold(gray, 50, 255, cv2.THRESH_BINARY_INV)
            contours, _ = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            if contours:
                best_contour = max(contours, key=cv2.contourArea)
                enclosing_circle_ratio = calculate_enclosing_circle_ratio(best_contour)
                smooth_enclosing_circle_ratio = moving_average_filter(enclosing_circle_ratio_values,
                                                                      enclosing_circle_ratio)

                # Compute enclosing circle
                (x, y), radius = cv2.minEnclosingCircle(best_contour)
                center = (int(x), int(y))
                radius = int(radius)

                # Draw enclosing circle
                cv2.circle(img, center, radius, (0, 255, 0), 1)

                # Draw contour
                cv2.drawContours(img, [best_contour], -1, (255, 0, 0), 1)  # Blue contour

                # Compute sector-wise fill ratios with smoothed sector areas
                sector_ratios = get_sector_ratios(best_contour, center, radius, sector_area_buffers)

                # Smooth each sector ratio
                smoothed_sector_ratios = np.zeros(12)
                for i in range(12):
                    smoothed_sector_ratios[i] = moving_average_filter(sector_ratio_values[i], sector_ratios[i])

                # Display text information on the left side
                draw_text_info(img, smooth_enclosing_circle_ratio, smoothed_sector_ratios)

            cv2.imshow('Blob Detection', img)

        grab_result.Release()

    camera.StopGrabbing()
    cv2.destroyAllWindows()


if __name__ == '__main__':
    main()

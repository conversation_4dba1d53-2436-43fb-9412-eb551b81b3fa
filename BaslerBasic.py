from pypylon import pylon
import cv2
import warnings
import numpy as np

warnings.simplefilter('ignore', DeprecationWarning)

def calculate_tenengrad(image):
    """
    Calculate sharpness using the Tenengrad method (gradient magnitude).
    """
    # Convert the image to grayscale
    gray_image = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

    # Calculate gradients using Sobel operators
    sobel_x = cv2.<PERSON>bel(gray_image, cv2.CV_64F, 1, 0, ksize=3)
    sobel_y = cv2.Sobel(gray_image, cv2.CV_64F, 0, 1, ksize=3)

    # Compute the gradient magnitude
    gradient_magnitude = np.sqrt(sobel_x ** 2 + sobel_y ** 2)

    # Compute the mean gradient magnitude as the sharpness score
    sharpness = np.mean(gradient_magnitude)
    return sharpness

def main():
    # Connecting to the first available camera
    camera = pylon.InstantCamera(pylon.TlFactory.GetInstance().CreateFirstDevice())
    camera.Open()

    camera.ExposureAuto.SetValue("Off")  # Disable automatic exposure
    camera.ExposureTime.SetValue(100000.0)  # Set exposure time to 10,000 µs (adjust as needed)

    # Set image format
    camera.Width.Value = 1920
    camera.Height.Value = camera.Height.Max

    # Get the maximum width and height
    width = camera.Width.Max

    # Center the image by setting the offset
    camera.OffsetX.Value = (width - 1920) // 2

    # Grabbing continuously (video) with minimal delay
    camera.StartGrabbing(pylon.GrabStrategy_LatestImageOnly)
    converter = pylon.ImageFormatConverter()

    # Converting to OpenCV BGR format
    converter.OutputPixelFormat = pylon.PixelType_BGR8packed
    converter.OutputBitAlignment = pylon.OutputBitAlignment_MsbAligned

    while camera.IsGrabbing():
        grab_result = camera.RetrieveResult(5000, pylon.TimeoutHandling_ThrowException)

        key = cv2.waitKey(1) & 0xFF
        if key == 27:  # If 'Esc' is pressed, break the loop
            break

        if grab_result.GrabSucceeded():
            # Access the image data
            image = converter.Convert(grab_result)
            img = image.GetArray()

            # Calculate sharpness using Tenengrad method
            sharpness = calculate_tenengrad(img)

            # Overlay the sharpness score on the image
            overlay_text = f"Sharpness: {sharpness:.2f}"
            cv2.putText(img, overlay_text, (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)

            # Display the raw image with sharpness overlay
            cv2.imshow('Raw Image', img)

        grab_result.Release()

    # Releasing the resource
    camera.StopGrabbing()
    cv2.destroyAllWindows()

if __name__ == '__main__':
    main()
import os
import shutil
from tkinter import Tk
from tkinter.filedialog import askdirectory


def merge_folders_to_class_folders(grandparent_folder):
    """
    Merges folders starting from a grandparent folder containing multiple parent folders,
    and consolidates all classification subfolders into a 'Merged' folder within the grandparent folder.
    <PERSON>les duplicate filenames by renaming them with unique identifiers and copies files instead of moving them.

    :param grandparent_folder: The grandparent folder containing multiple parent folders.
    """
    # Create the 'Merged' target folder inside the grandparent folder
    target_folder = os.path.join(grandparent_folder, "Merged")
    os.makedirs(target_folder, exist_ok=True)

    # Traverse all parent folders under the grandparent folder
    for parent_folder in os.listdir(grandparent_folder):
        parent_folder_path = os.path.join(grandparent_folder, parent_folder)

        # Ensure it's a directory and skip the 'Merged' folder
        if os.path.isdir(parent_folder_path) and parent_folder != "Merged":
            print(f"Processing parent folder: {parent_folder_path}")

            # Walk through the classification subfolders in the parent folder
            for root, subdirs, files in os.walk(parent_folder_path):
                for file in files:
                    # Construct the source file path
                    source_file_path = os.path.join(root, file)

                    # Determine the classification folder name (e.g., "ClassA", "ClassB")
                    relative_path = os.path.relpath(root, parent_folder_path)
                    class_folder = relative_path.split(os.sep)[0]  # Get the top-level subfolder name

                    # Create the corresponding class folder in the target directory
                    destination_subfolder = os.path.join(target_folder, class_folder)
                    os.makedirs(destination_subfolder, exist_ok=True)

                    # Generate a unique filename if a conflict occurs
                    target_file_path = os.path.join(destination_subfolder, file)
                    if os.path.exists(target_file_path):
                        file_name, file_ext = os.path.splitext(file)
                        unique_counter = 1
                        while os.path.exists(target_file_path):
                            # Append a counter to make the filename unique
                            new_file_name = f"{file_name}_{unique_counter}{file_ext}"
                            target_file_path = os.path.join(destination_subfolder, new_file_name)
                            unique_counter += 1

                    # Copy the file to the target folder
                    shutil.copy2(source_file_path, target_file_path)
                    print(f"Copied: {source_file_path} -> {target_file_path}")

    print(f"All folders have been successfully merged into: {target_folder}")


def main():
    # Ask the user to select the grandparent folder
    Tk().withdraw()  # Hide the main tkinter window
    grandparent_folder = askdirectory(title="Select Grandparent Folder")

    if not grandparent_folder:
        print("No grandparent folder selected. Exiting.")
        return

    # Perform the merge
    merge_folders_to_class_folders(grandparent_folder)


if __name__ == "__main__":
    main()

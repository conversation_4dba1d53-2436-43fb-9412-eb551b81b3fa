import os
import cv2
import numpy as np
import tkinter as tk
from tkinter import filedialog
from openvino.runtime import Core
from natsort import natsorted

# Load OpenVINO model
ie = Core()
model_path = "openvino_model/yolov11.xml"
compiled_model = ie.compile_model(model=model_path, device_name="CPU")

# Get input and output layer names
input_layer = compiled_model.input(0)
output_layer = compiled_model.output(0)

# Open file dialog to select a folder
root = tk.Tk()
root.withdraw()
folder_selected = filedialog.askdirectory(title="Select a folder with images")

if not folder_selected:
    print("No folder selected. Exiting...")
    exit()

# Get all image files in the folder
image_extensions = (".jpg", ".jpeg", ".png", ".bmp", ".tiff")
image_files = [f for f in os.listdir(folder_selected) if f.lower().endswith(image_extensions)]
image_files = natsorted(image_files)

if not image_files:
    print("No image files found in the selected folder. Exiting...")
    exit()

print(f"Found {len(image_files)} images. Processing...")

# Define thresholds for PCD validation
PCD_MIN = 275
PCD_MAX = 350
PCD_CENTER_THRESHOLD = 20

def calculate_circumcircle(p1, p2, p3):
    """ Calculate the circumcircle of three points. """
    A, B, C = np.array(p1), np.array(p2), np.array(p3)
    midAB, midBC = (A + B) / 2, (B + C) / 2
    AB_perp, BC_perp = np.array([- (B - A)[1], (B - A)[0]]), np.array([- (C - B)[1], (C - B)[0]])
    try:
        t = np.linalg.solve(np.vstack([AB_perp, -BC_perp]).T, midBC - midAB)
        circumcenter = midAB + t[0] * AB_perp
    except np.linalg.LinAlgError:
        return None, None
    return circumcenter.astype(int), 2 * np.linalg.norm(circumcenter - A)

for img_file in image_files:
    img_path = os.path.join(folder_selected, img_file)
    image = cv2.imread(img_path)

    # Preprocess image for OpenVINO
    input_image = cv2.resize(image, (640, 640))
    input_image = input_image.transpose(2, 0, 1)  # HWC to CHW
    input_image = np.expand_dims(input_image, axis=0).astype(np.float32)

    # Run OpenVINO inference
    result = compiled_model([input_image])[output_layer]

    # Process detections
    output_img = image.copy()
    detected_objects = []
    petal_centers = []
    plunger_center = None
    toenail_found = False
    bend_found = False

    for detection in result[0]:
        confidence = detection[4]  # Confidence score
        if confidence < 0.25:
            continue  # Skip low-confidence detections

        x_min, y_min, x_max, y_max = (detection[:4] * [image.shape[1], image.shape[0], image.shape[1], image.shape[0]]).astype(int)
        class_id = int(detection[5])  # Class index
        class_name = f"Class_{class_id}"  # Replace with actual class names if available
        detected_objects.append(class_name)

        center_x = (x_min + x_max) // 2
        center_y = (y_min + y_max) // 2

        if class_name == "Petal":
            petal_centers.append((center_x, center_y))
        elif class_name == "Plunger":
            plunger_center = (center_x, center_y)
        elif class_name == "Toenail":
            toenail_found = True
        elif class_name == "Bend":
            bend_found = True

        # Draw bounding boxes
        color = (0, 255, 0) if class_name == "Plunger" else (0, 0, 255)
        cv2.rectangle(output_img, (x_min, y_min), (x_max, y_max), color, 2)
        cv2.putText(output_img, f"{class_name} {confidence:.2f}", (x_min, y_min - 5), cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2)

    # Check conditions for PCD calculation
    valid_pcd = False
    best_circumcenter, best_diameter = None, None

    if len(petal_centers) == 3:
        print("Exactly 3 petals found. Calculating PCD...")
        best_circumcenter, best_diameter = calculate_circumcircle(*petal_centers)
        if best_circumcenter is not None and plunger_center is not None:
            pcd_distance = np.linalg.norm(np.array(best_circumcenter) - np.array(plunger_center))
            print(f"PCD: {best_diameter:.2f}, Distance from Plunger: {pcd_distance:.2f}")
            valid_pcd = (PCD_MIN <= best_diameter <= PCD_MAX) and (pcd_distance <= PCD_CENTER_THRESHOLD)

        if best_circumcenter is not None:
            cv2.circle(output_img, best_circumcenter, int(best_diameter / 2), (255, 0, 0), 2)
            cv2.putText(output_img, f"PCD: {best_diameter:.2f}, Distance: {pcd_distance:.2f}",
                        (best_circumcenter[0] - 50, best_circumcenter[1] - 10),
                        cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 0, 0), 2)

    cv2.imshow("OpenVINO Inference", output_img)
    print(f"Processed: {img_file}")

    # Decision logic
    if len(petal_centers) != 3 or not plunger_center or toenail_found or bend_found or not valid_pcd:
        if not (plunger_center or petal_centers):  # Skip waiting if empty position
            print("No plunger or petals found. Auto-advancing...")
            cv2.waitKey(1)
        else:
            print("Condition not met: Waiting for key press...")
            key = cv2.waitKey(0) & 0xFF
            if key == ord('q'):
                print("Exiting early...")
                cv2.destroyAllWindows()
                exit()
    else:
        cv2.waitKey(1)  # Auto-advance if conditions are met

cv2.destroyAllWindows()
print("Inference completed for all images.")

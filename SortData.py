'''
A simple script to sort images into different folders based on user input.
'''

import os
import cv2
import shutil
from tkinter import Tk
from tkinter.filedialog import askdirectory
import re


def resize_image(image, max_height=1000):
    height, width = image.shape[:2]
    if height > max_height:
        scaling_factor = max_height / height
        new_width = int(width * scaling_factor)
        resized_image = cv2.resize(image, (new_width, max_height))
        return resized_image
    return image


def natural_sort_key(s):
    """
    Sort string in a human-readable way (e.g., file1, file2, file10).
    """
    return [int(text) if text.isdigit() else text.lower() for text in re.split(r'(\d+)', s)]


def add_legend(image, position_text):
    """
    Add position and legend text overlay on the image.
    """
    font = cv2.FONT_HERSHEY_SIMPLEX
    font_scale = 0.6
    font_color = (255, 255, 255)  # White
    bg_color = (0, 0, 0)  # Black background for text
    thickness = 1

    # Add the position text
    text_y_offset = 20
    text_x = 10
    text_y = text_y_offset
    text_size = cv2.getTextSize(position_text, font, font_scale, thickness)[0]
    cv2.rectangle(image, (text_x, text_y - text_size[1] - 5), (text_x + text_size[0] + 5, text_y + 5), bg_color, -1)
    cv2.putText(image, position_text, (text_x, text_y), font, font_scale, font_color, thickness)

    # Add the legend
    legend_lines = [
        "E: Move to Empty",
        "O: Move to OK",
        "N: Move to NG",
        "U: Move to Unsure",
        "B: Back to Previous",
        "ESC: Exit"
    ]
    legend_start_y = text_y + 30
    for i, legend_text in enumerate(legend_lines):
        text_y = legend_start_y + i * 20
        text_size = cv2.getTextSize(legend_text, font, font_scale, thickness)[0]
        cv2.rectangle(
            image,
            (text_x, text_y - text_size[1] - 5),
            (text_x + text_size[0] + 5, text_y + 5),
            bg_color,
            -1
        )
        cv2.putText(image, legend_text, (text_x, text_y), font, font_scale, font_color, thickness)


def move_image_to_folder(image_path, destination_folder):
    """
    Move the image to the specified folder.
    """
    os.makedirs(destination_folder, exist_ok=True)
    destination_path = os.path.join(destination_folder, os.path.basename(image_path))
    shutil.move(image_path, destination_path)
    return destination_path


def main():
    # Ask the user to select a folder
    Tk().withdraw()  # Hide the main tkinter window
    folder_path = askdirectory(title="Select Folder with Images")

    if not folder_path:
        print("No folder selected. Exiting.")
        return

    # Create subfolders for classification if they don't exist
    empty_folder = os.path.join(folder_path, "Empty")
    ok_folder = os.path.join(folder_path, "OK")
    ng_folder = os.path.join(folder_path, "NG")
    unsure_folder = os.path.join(folder_path, "Unsure")  # New Unsure folder

    os.makedirs(empty_folder, exist_ok=True)
    os.makedirs(ok_folder, exist_ok=True)
    os.makedirs(ng_folder, exist_ok=True)
    os.makedirs(unsure_folder, exist_ok=True)

    # Get list of images in the folder and sort them naturally
    image_files = sorted(
        [f for f in os.listdir(folder_path) if f.lower().endswith((".jpg", ".jpeg", ".png", ".bmp", ".gif"))],
        key=natural_sort_key
    )

    if not image_files:
        print("No images found in the selected folder.")
        return

    total_images = len(image_files)
    index = 0  # Initialize the index to track the current image
    classified_images = {}  # Dictionary to track already classified images and their new locations

    while 0 <= index < total_images:
        image_file = image_files[index]

        # Determine the current location of the image (classified or original folder)
        if image_file in classified_images:
            image_path = classified_images[image_file]
        else:
            image_path = os.path.join(folder_path, image_file)

        # Try to load the image
        image = cv2.imread(image_path)

        if image is None:
            print(f"Skipping invalid image: {image_file}")
            index += 1
            continue

        # Resize image if needed
        resized_image = resize_image(image)

        # Add position and legend overlay
        position_text = f"Image {index + 1} of {total_images}"
        add_legend(resized_image, position_text)

        # Display the image
        cv2.imshow("Image Classification", resized_image)

        # Wait for key press
        key = cv2.waitKey(0) & 0xFF

        if key == 27:  # ESC key to quit
            print("Exiting...")
            break
        elif key == ord('e'):  # E for Empty
            new_path = move_image_to_folder(image_path, empty_folder)
            classified_images[image_file] = new_path
            print(f"Moved {image_file} to Empty folder.")
            index += 1
        elif key == ord('o'):  # O for OK
            new_path = move_image_to_folder(image_path, ok_folder)
            classified_images[image_file] = new_path
            print(f"Moved {image_file} to OK folder.")
            index += 1
        elif key == ord('n'):  # N for NG
            new_path = move_image_to_folder(image_path, ng_folder)
            classified_images[image_file] = new_path
            print(f"Moved {image_file} to NG folder.")
            index += 1
        elif key == ord('u'):  # U for Unsure
            new_path = move_image_to_folder(image_path, unsure_folder)
            classified_images[image_file] = new_path
            print(f"Moved {image_file} to Unsure folder.")
            index += 1
        elif key == ord('b'):  # B for Back
            if index > 0:
                index -= 1
                print(f"Going back to previous image: {image_files[index]}")
            else:
                print("Already at the first image. Can't go back.")

    cv2.destroyAllWindows()


if __name__ == "__main__":
    main()

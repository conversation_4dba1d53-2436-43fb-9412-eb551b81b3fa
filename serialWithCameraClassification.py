import serial
import time
import json
import cv2
import numpy as np
import serial.tools.list_ports
import warnings
import tensorflow
from PIL import Image
import os
import csv
from tkinter import Tk
from tkinter.filedialog import asksaveasfilename

import absl.logging

# Suppress TensorFlow and Abseil warnings
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'  # Suppress TensorFlow INFO and WARNING logs
absl.logging.set_verbosity(absl.logging.ERROR)

warnings.simplefilter('ignore', DeprecationWarning)

def find_reprap_serial_port():
    ports = list(serial.tools.list_ports.comports())
    for port in ports:
        if "RepRapFirmware" in port.description:
            print(f"Found RepRapFirmware port: {port.device}")
            return port.device
    print("Error: Could not find RepRapFirmware serial port.")
    return None

def send_gcode(serial_port, command):
    try:
        serial_port.write((command + '\n').encode())
        responses = []
        while True:
            response = serial_port.readline().decode().strip()
            if response:
                responses.append(response)
                if "ok" in response:
                    break
        return responses
    except serial.SerialException as e:
        print(f"Error sending '{command}': {e}")
        return []

def parse_status_response(response):
    try:
        return json.loads(response)
    except json.JSONDecodeError:
        print("Error decoding JSON response")
        return None

def wait_for_idle(serial_port):
    try:
        last_status = None
        while True:
            responses = send_gcode(serial_port, "M408 S0")
            if not responses:
                time.sleep(0.2)
                continue
            for response in responses:
                if response.startswith("{") and response.endswith("}"):
                    status_data = parse_status_response(response)
                    if status_data:
                        current_status = status_data.get("status")
                        if current_status == "I":
                            print("Printer is idle.")
                            return
                        elif current_status == "B" and last_status != "B":
                            print("Printer is busy... Waiting.")
                        last_status = current_status
            time.sleep(0.2)
    except serial.SerialException as e:
        print(f"Error querying status: {e}")

def get_axis_limits(serial_port):
    """
    Queries the printer for axis limits using M208 and returns them as a dictionary.
    Stops execution if parsing fails.
    """
    responses = send_gcode(serial_port, "M208")
    axis_limits = {}

    for response in responses:
        if "Axis limits" in response:
            try:
                # Remove the "Axis limits (mm)" prefix
                response = response.replace("Axis limits (mm) ", "").strip()

                # Split into parts for each axis
                parts = response.split(", ")
                for part in parts:
                    # Each part should look like 'X0.0:230.0'
                    axis = part[0]  # First character is the axis (e.g., X, Y, Z)
                    limits = part[1:]  # Remaining is the limits (e.g., 0.0:230.0)
                    min_val, max_val = map(float, limits.split(":"))
                    axis_limits[axis] = (min_val, max_val)

            except (ValueError, IndexError) as e:
                print(f"Error parsing axis limits: {e}")
                print(f"Failed part: {part}")
                print("Stopping execution due to critical error in parsing axis limits.")
                exit(1)

    # Ensure all axis limits are parsed
    if not all(axis in axis_limits for axis in ["X", "Y", "Z"]):
        print("Error: Missing axis limits after parsing.")
        exit(1)

    return axis_limits

def calculate_tenengrad(image):
    """
    Calculate sharpness using the Tenengrad method (gradient magnitude).
    """
    # Convert the image to grayscale
    gray_image = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

    # Calculate gradients using Sobel operators
    sobel_x = cv2.Sobel(gray_image, cv2.CV_64F, 1, 0, ksize=3)
    sobel_y = cv2.Sobel(gray_image, cv2.CV_64F, 0, 1, ksize=3)

    # Compute the gradient magnitude
    gradient_magnitude = np.sqrt(sobel_x ** 2 + sobel_y ** 2)

    # Compute the mean gradient magnitude as the sharpness score
    sharpness = np.mean(gradient_magnitude)
    return sharpness

def coarse_autofocus(serial_port, camera, z_start, z_end, z_feedrate=500):
    print("Starting continuous autofocus sequence...")

    send_gcode(serial_port, f"G1 Z{z_start} F2000")
    wait_for_idle(serial_port)

    start_time = time.time()
    send_gcode(serial_port, f"G1 Z{z_end} F{z_feedrate}")
    print(f"Moving Z-axis from {z_start} to {z_end} at feedrate {z_feedrate}")

    best_sharpness = 0
    best_z = z_start
    current_z = z_start

    motion_duration = abs(z_start - z_end) / (z_feedrate / 60)

    while time.time() - start_time < motion_duration:
        ret, frame = camera.read()
        if ret:
            cv2.imshow("Live View", frame)  # Display the live image
            cv2.waitKey(1)  # Refresh the display window
            sharpness = calculate_tenengrad(frame)
            print(f"Z: {current_z}, Sharpness: {sharpness}")

            if sharpness > best_sharpness:
                best_sharpness = sharpness
                best_z = current_z

        elapsed_time = time.time() - start_time
        current_z = z_start + (z_feedrate / 60) * elapsed_time if z_start < z_end else z_start - (z_feedrate / 60) * elapsed_time

    send_gcode(serial_port, f"G1 Z{best_z} F2000")
    wait_for_idle(serial_port)
    print(f"Continuous autofocus complete. Best Z position: {best_z}")
    return best_z

def fine_tune_autofocus(serial_port, camera, coarse_z, z_range=5, z_step=0.1, z_feedrate=100, stop_threshold=5, rise_threshold=5):
    print("Starting fine-tune autofocus sequence...")

    fine_start = coarse_z - z_range / 2
    fine_end = coarse_z + z_range / 2

    best_sharpness = 0
    best_z = coarse_z

    current_z = fine_start
    drop_count = 0
    rise_count = 0
    last_sharpness = 0

    while current_z <= fine_end:
        send_gcode(serial_port, f"G1 Z{current_z:.2f} F{z_feedrate}")
        wait_for_idle(serial_port)

        ret, frame = camera.read()
        if ret:
            cv2.imshow("Live View", frame)  # Display the live image
            cv2.waitKey(1)  # Refresh the display window
            sharpness = calculate_tenengrad(frame)
            print(f"Z: {current_z:.2f}, Sharpness: {sharpness:.2f}")

            if sharpness > last_sharpness:
                rise_count += 1
                drop_count = 0  # Reset drop count since sharpness improved
                if rise_count >= rise_threshold:
                    best_sharpness = sharpness
                    best_z = current_z
            elif sharpness < last_sharpness:
                drop_count += 1
                if drop_count >= stop_threshold and rise_count >= rise_threshold:
                    print("Sharpness peaked and started dropping after sufficient rise. Stopping fine-tune scan early.")
                    break

            last_sharpness = sharpness

        current_z += z_step

    send_gcode(serial_port, f"G1 Z{best_z:.2f} F{z_feedrate}")
    wait_for_idle(serial_port)

    print(f"Fine-tune autofocus complete. Best Z position: {best_z:.2f}")
    return best_z

def classify_image(model, frame, classes):
    """
    Classify the image frame using the TensorFlow model.

    Args:
        model: Loaded TensorFlow model.
        frame: The image frame to classify (numpy array).
        classes: List of class labels.

    Returns:
        Classification result (e.g., "Good", "No good", "Empty").
    """
    try:
        # Resize the frame for the model input
        img = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)  # Convert to RGB format
        img_pil = Image.fromarray(img)  # Convert to PIL Image
        img_resized = img_pil.resize((300, 300), Image.Resampling.LANCZOS)  # Use LANCZOS for resizing

        # Prepare the image for the model
        inp_numpy = np.array(img_resized)[None]  # Add batch dimension
        inp = tensorflow.constant(inp_numpy, dtype='float32')

        # Run inference
        class_scores = model(inp)[0].numpy()
        classification = classes[class_scores.argmax()]

        return classification
    except Exception as e:
        print(f"Error during classification: {e}")
        return "Error"

def zigzag_move_with_classification(serial_port, camera, step_x, step_y, num_columns, num_rows, speed, acceleration,
                                    visible_percent, model, classes):
    """
    Performs zigzag motion, updates live view, and prompts to save classification results after completion.

    Args:
        serial_port: Serial port for printer communication.
        camera: OpenCV camera object.
        step_x: Step size in the X direction.
        step_y: Step size in the Y direction.
        num_columns: Number of columns to traverse.
        num_rows: Number of rows to traverse.
        speed: Movement speed.
        acceleration: Acceleration for movements.
        visible_percent: Percentage of the image to keep visible for classification.
        model: TensorFlow model for classification.
        classes: List of class labels.
    """
    # List to store results in memory before saving
    results = []

    send_gcode(serial_port, f"M204 P{acceleration} T{acceleration}")
    send_gcode(serial_port, "G1 X56 Y147 F6000")
    wait_for_idle(serial_port)

    current_y = 147
    start_x = 56
    feedrate = f"F{speed}"

    for row in range(num_rows):
        row_label = chr(65 + row)
        if row % 2 == 0:
            for col in range(num_columns):
                target_x = start_x + (col * step_x)
                send_gcode(serial_port, f"G1 X{target_x:.2f} Y{current_y:.2f} {feedrate}")
                wait_for_idle(serial_port)
                time.sleep(0.3)

                # Capture and update live view
                ret, frame = camera.read()
                if ret:
                    # Apply the configurable mask to the image
                    masked_frame = mask_image_configurable(frame, visible_percent)
                    # Display the live view
                    cv2.imshow("Live View", masked_frame)
                    cv2.waitKey(1)  # Refresh the display

                    # Perform classification
                    class_scores = model(tensorflow.constant(np.array(masked_frame)[None], dtype="float32"))[0].numpy()
                    classification = classes[class_scores.argmax()]
                    confidence = class_scores.max()

                    # Store the result in memory
                    position = f"{row_label}{col + 1}"
                    results.append([position, classification, confidence])
                    print(f"Position: {position}, Classification: {classification}, Confidence: {confidence:.2f}")
        else:
            for col in reversed(range(num_columns)):
                target_x = start_x + (col * step_x)
                send_gcode(serial_port, f"G1 X{target_x:.2f} Y{current_y:.2f} {feedrate}")
                wait_for_idle(serial_port)
                time.sleep(0.3)

                # Capture and update live view
                ret, frame = camera.read()
                if ret:
                    # Apply the configurable mask to the image
                    masked_frame = mask_image_configurable(frame, visible_percent)
                    # Display the live view
                    cv2.imshow("Live View", masked_frame)
                    cv2.waitKey(1)  # Refresh the display

                    # Perform classification
                    class_scores = model(tensorflow.constant(np.array(masked_frame)[None], dtype="float32"))[0].numpy()
                    classification = classes[class_scores.argmax()]
                    confidence = class_scores.max()

                    # Store the result in memory
                    position = f"{row_label}{col + 1}"
                    results.append([position, classification, confidence])
                    print(f"Position: {position}, Classification: {classification}, Confidence: {confidence:.2f}")

        current_y -= step_y
        send_gcode(serial_port, f"G1 Y{current_y:.2f} {feedrate}")
        wait_for_idle(serial_port)

    # Close the live view window
    cv2.destroyAllWindows()

    # Prompt the user to save the CSV file
    print("Zigzag movement complete. Saving results...")
    Tk().withdraw()  # Hide the main tkinter window
    output_csv = asksaveasfilename(
        defaultextension=".csv",
        filetypes=[("CSV files", "*.csv"), ("All files", "*.*")],
        title="Save Classification Results As"
    )
    if not output_csv:
        print("No file selected. Results were not saved.")
        return

    # Save results to the chosen file
    with open(output_csv, mode="w", newline="") as file:
        writer = csv.writer(file)
        writer.writerow(["Position", "Classification", "Confidence"])  # Write header
        writer.writerows(results)  # Write results
    print(f"Inference results saved to {output_csv}")


def mask_image_configurable(image, visible_percent=33):
    """
    Masks the outer edges of an image, keeping only a configurable central region visible.

    Args:
        image: Input image (numpy array).
        visible_percent: Percentage of the image to keep visible (integer between 0 and 100).

    Returns:
        Masked image with only the specified percentage of the center visible.
    """
    if visible_percent <= 0 or visible_percent > 100:
        raise ValueError("visible_percent must be between 1 and 100.")

    height, width, _ = image.shape

    # Calculate the margins based on the visible percentage
    visible_fraction = visible_percent / 100
    y_margin = int((1 - visible_fraction) * height / 2)
    x_margin = int((1 - visible_fraction) * width / 2)

    # Define the central visible region
    y_start = y_margin
    y_end = height - y_margin
    x_start = x_margin
    x_end = width - x_margin

    # Create a mask
    mask = np.zeros((height, width), dtype=np.uint8)
    mask[y_start:y_end, x_start:x_end] = 255  # Keep the central region

    # Apply the mask to the image
    masked_image = cv2.bitwise_and(image, image, mask=mask)

    return masked_image

# Initialize the TensorFlow model and class labels
model = tensorflow.saved_model.load('./')
classes = ["Empty", "Good", "No good"]

#SERIAL_PORT = find_reprap_serial_port()
SERIAL_PORT = "COM5"
if SERIAL_PORT is None:
    print("No valid serial port found. Exiting.")
    exit(1)

BAUD_RATE = 115200
STEP_X = 4.5
STEP_Y = 4.5
NUM_COLUMNS = 24
NUM_ROWS = 16
SPEED = 6000
ACCELERATION = 1000

try:
    with serial.Serial(SERIAL_PORT, BAUD_RATE, timeout=1) as ser:
        # OpenCV USB camera initialization (0 typically refers to the default camera)
        camera = cv2.VideoCapture(1)
        if not camera.isOpened():
            print("Error: Could not open USB camera.")
            exit(1)

        send_gcode(ser, "G28") # Home all axes
        wait_for_idle(ser) # Wait until the printer is idle

        axis_limits = get_axis_limits(ser) # Get axis limits
        print(f"Axis limits: {axis_limits}")
        z_start, z_end = axis_limits["Z"] # Get Z-axis limit

        send_gcode(ser, f"G1 X56 Y147 F6000") # Move to the starting XY position
        wait_for_idle(ser)

        best_z = coarse_autofocus(ser, camera, z_start, z_end) # Perform coarse autofocus
        send_gcode(ser, f"G1 Z{best_z} F2000") # Move to the best Z position
        wait_for_idle(ser)

        fine_tuned_z = fine_tune_autofocus(ser, camera, best_z, z_range=10, z_step=0.1, z_feedrate=6000)
        send_gcode(ser, f"G1 Z{fine_tuned_z} F2000")
        wait_for_idle(ser)

        zigzag_move_with_classification(
            serial_port=ser,
            camera=camera,
            step_x=STEP_X,
            step_y=STEP_Y,
            num_columns=NUM_COLUMNS,
            num_rows=NUM_ROWS,
            speed=SPEED,
            acceleration=ACCELERATION,
            visible_percent=60,
            model=model,
            classes=classes
        )

        send_gcode(ser, "G28") # Home all axes
        wait_for_idle(ser)

        camera.release()  # Release the USB camera
except serial.SerialException as e:
    print(f"Error opening serial port: {e}")

from fastai.vision.all import *
from timm import create_model  # Import timm for EfficientNet
from pathlib import Path

def train_model():
    # 1. Define the dataset path
    path = Path("data")  # Replace with your dataset directory

    # 2. Load the data
    dls = ImageDataLoaders.from_folder(
        path,
        valid_pct=0.2,         # Use 20% of data for validation
        item_tfms=Resize(224),  # Resize all images to 224x224
        batch_tfms=aug_transforms(flip_vert=True)  # Add basic data augmentation
    )

    # Optional: Show a batch of images (Uncomment to debug)
    dls.show_batch(max_n=9, figsize=(6, 6))

    # 3. Define the EfficientNet model
    def efficientnet():
        return create_model('efficientnet_b0', pretrained=True, num_classes=dls.c)

    # 4. Create the learner with EfficientNet
    learn = Learner(
        dls,
        efficientnet(),
        loss_func=CrossEntropyLossFlat(),  # Cross-entropy for classification
        metrics=accuracy  # Use accuracy as the evaluation metric
    )

    # 5. Find the optimal learning rate
    learn.lr_find()
    # Uncomment the line below to display the suggested learning rate:
    learn.recorder.plot_lr_find()

    # 6. Train the model
    learn.fine_tune(4, base_lr=1e-3)  # Fine-tune for 4 epochs

    # 7. Save the trained model
    learn.export("good_bad_classifier_efficientnet.pkl")

    print("Training completed and model saved as 'good_bad_classifier_efficientnet.pkl'")

if __name__ == '__main__':
    train_model()

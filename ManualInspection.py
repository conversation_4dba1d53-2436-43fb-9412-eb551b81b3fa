import os
import serial
import time
import json
import cv2
import serial.tools.list_ports
from datetime import datetime
from pypylon import pylon
import warnings

warnings.simplefilter('ignore', DeprecationWarning)

SAVE_IMAGES = True  # Set this flag to True to save captured images

SERIAL_PORT = "COM5"
BAUD_RATE = 115200
STEP_X = 4.5
STEP_Y = 4.5
NUM_COLUMNS = 24
NUM_ROWS = 16
SPEED = 6000
ACCELERATION = 1000
START_X = 46.6
START_Y = 148.1
START_Z = 59
Z_INCREMENT = 0.05


def send_gcode(serial_port, command):
    try:
        serial_port.write((command + '\n').encode())
        responses = []
        while True:
            response = serial_port.readline().decode().strip()
            if response:
                responses.append(response)
                if "ok" in response:
                    break
        return responses
    except serial.SerialException as e:
        print(f"Error sending '{command}': {e}")
        return []


def parse_status_response(response):
    try:
        return json.loads(response)
    except json.JSONDecodeError:
        print("Error decoding JSON response")
        return None


def wait_for_idle(serial_port):
    try:
        last_status = None
        while True:
            responses = send_gcode(serial_port, "M408 S0")
            if not responses:
                time.sleep(0.2)
                continue
            for response in responses:
                if response.startswith("{") and response.endswith("}"):
                    status_data = parse_status_response(response)
                    if status_data:
                        current_status = status_data.get("status")
                        if current_status == "I":
                            print("Printer is idle.")
                            return
                        elif current_status == "B" and last_status != "B":
                            print("Printer is busy... Waiting.")
                        last_status = current_status
            time.sleep(0.2)
    except serial.SerialException as e:
        print(f"Error querying status: {e}")


def show_resized_image(window_name, frame, max_width=1920, max_height=1080):
    """
    Display a resized image to fit within the specified max dimensions.
    """
    height, width = frame.shape[:2]
    scale = min(max_width / width, max_height / height)
    new_width = int(width * scale)
    new_height = int(height * scale)
    resized_frame = cv2.resize(frame, (new_width, new_height), interpolation=cv2.INTER_AREA)
    cv2.imshow(window_name, resized_frame)


def adjust_z_position(serial_port, camera, converter, z_start, z_increment):
    """
    Adjust the Z-axis position using arrow keys and display the live camera feed.
    Press Enter to confirm the position and continue.
    """
    current_z = z_start
    send_gcode(serial_port, f"G1 Z{current_z:.2f} F2000")
    wait_for_idle(serial_port)

    print("Adjust Z-axis position using W (up) and S (down). Press Enter to confirm.")

    while True:
        grab_result = camera.RetrieveResult(5000, pylon.TimeoutHandling_ThrowException)
        if grab_result.GrabSucceeded():
            image = converter.Convert(grab_result)
            frame = image.GetArray()
            show_resized_image("Live View", frame)
            key = cv2.waitKey(1)
            if key == 27:  # ESC to exit
                print("Exiting Z adjustment mode.")
                break
            elif key == 13:  # Enter key to confirm
                print(f"Z-axis position confirmed at {current_z:.2f}.")
                break
            elif key == ord('w'):  # 'W' key to move Z up
                current_z += z_increment
                print(f"Moving Z up to {current_z:.2f}.")
                send_gcode(serial_port, f"G1 Z{current_z:.2f} F2000")
                wait_for_idle(serial_port)
            elif key == ord('s'):  # 'S' key to move Z down
                current_z -= z_increment
                print(f"Moving Z down to {current_z:.2f}.")
                send_gcode(serial_port, f"G1 Z{current_z:.2f} F2000")
                wait_for_idle(serial_port)
        grab_result.Release()


def zigzag_move(serial_port, camera, converter, step_x, step_y, num_columns, num_rows, speed, acceleration, start_x,
                start_y, save_images):
    if save_images:
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        os.makedirs(timestamp, exist_ok=True)

    # Set acceleration and move to start position
    send_gcode(serial_port, f"M204 P{acceleration} T{acceleration}")
    send_gcode(serial_port, f"G1 X{start_x} Y{start_y} F6000")
    wait_for_idle(serial_port)

    current_y = start_y
    feedrate = f"F{speed}"

    # Loop through each row
    for row in range(num_rows):
        row_label = chr(65 + row)
        # Depending on row parity, iterate columns forward or backward.
        if row % 2 == 0:
            col_range = range(num_columns)
        else:
            col_range = reversed(range(num_columns))

        for col in col_range:
            target_x = start_x + (col * step_x)
            # Move to next position
            send_gcode(serial_port, f"G1 X{target_x:.2f} Y{current_y:.2f} {feedrate}")
            wait_for_idle(serial_port)
            time.sleep(0.2)

            # Capture and display image, then wait for interactive decision.
            while True:
                try:
                    grab_result = camera.RetrieveResult(5000, pylon.TimeoutHandling_ThrowException)
                except Exception as e:
                    print(f"Error grabbing image: {e}")
                    break
                if grab_result.GrabSucceeded():
                    image = converter.Convert(grab_result)
                    frame = image.GetArray()
                    image_name = f"{row_label}{col + 1}.jpg"
                    show_resized_image("Live View", frame)
                    grab_result.Release()
                else:
                    print("Failed to grab image.")
                    break

                print("Press O for OK, N for NG (to adjust load), or ESC to exit.")
                decision_key = cv2.waitKey(0) & 0xFF
                # If ESC is pressed, exit the program.
                if decision_key == 27:
                    print("Exiting program on user request.")
                    exit(0)

                # If image is accepted, optionally save and break out of decision loop.
                if decision_key in [ord('o'), ord('O')]:
                    if save_images:
                        image_path = os.path.join(timestamp, image_name)
                        cv2.imwrite(image_path, frame)
                        print(f"Saved: {image_path}")
                    # Accept the image and break out of the while loop to move on.
                    break
                elif decision_key in [ord('n'), ord('N')]:
                    # Move Y axis +50 mm
                    current_y += 50
                    print("NG selected. Moving Y axis +50 mm for load adjustment.")
                    send_gcode(serial_port, f"G1 Y{current_y:.2f} {feedrate}")
                    wait_for_idle(serial_port)
                    print("Press L to load new image at adjusted position.")
                    # Wait for L key before moving back.
                    while True:
                        load_key = cv2.waitKey(0) & 0xFF
                        if load_key in [ord('l'), ord('L')]:
                            # Move Y axis -50 mm to return to original position.
                            current_y -= 50
                            send_gcode(serial_port, f"G1 Y{current_y:.2f} {feedrate}")
                            wait_for_idle(serial_port)
                            print("Loaded new image after adjustment.")
                            # After load, the while loop will re-capture a new image.
                            break
                        else:
                            print("Invalid key. Please press L to load the image.")
                    # Continue the outer while loop to re-capture and re-display the image.
                else:
                    print("Invalid key. Please press O for OK or N for NG.")
            # End of decision loop for current image

        # End of row: update Y for the next row.
        current_y -= step_y
        send_gcode(serial_port, f"G1 Y{current_y:.2f} {feedrate}")
        wait_for_idle(serial_port)


try:
    with serial.Serial(SERIAL_PORT, BAUD_RATE, timeout=1) as ser:
        camera = pylon.InstantCamera(pylon.TlFactory.GetInstance().CreateFirstDevice())
        camera.Open()

        converter = pylon.ImageFormatConverter()
        converter.OutputPixelFormat = pylon.PixelType_BGR8packed
        converter.OutputBitAlignment = pylon.OutputBitAlignment_MsbAligned

        camera.StartGrabbing(pylon.GrabStrategy_LatestImageOnly)

        send_gcode(ser, "G28")
        wait_for_idle(ser)

        send_gcode(ser, f"G1 X{START_X} Y{START_Y} F6000")
        wait_for_idle(ser)

        adjust_z_position(ser, camera, converter, START_Z, Z_INCREMENT)

        zigzag_move(ser, camera, converter, STEP_X, STEP_Y, NUM_COLUMNS, NUM_ROWS, SPEED, ACCELERATION, START_X,
                    START_Y, SAVE_IMAGES)

        send_gcode(ser, "G28")
        wait_for_idle(ser)

        camera.StopGrabbing()
except serial.SerialException as e:
    print(f"Error opening serial port: {e}")

import os
import cv2
import tkinter as tk
import numpy as np
from tkinter import filedialog
from ultralytics import YOL<PERSON>
from natsort import natsorted

# Load YOLO model with trained weights
model = YOLO("trained.pt")  # Replace with your YOLO weights file

# Open file dialog to select a folder
root = tk.Tk()
root.withdraw()  # Hide main window
folder_selected = filedialog.askdirectory(title="Select a folder with images")

# Check if a folder was selected
if not folder_selected:
    print("No folder selected. Exiting...")
    exit()

# Get all image files in the folder
image_extensions = (".jpg", ".jpeg", ".png", ".bmp", ".tiff")
image_files = [f for f in os.listdir(folder_selected) if f.lower().endswith(image_extensions)]
image_files = natsorted(image_files)  # Natural sorting

if not image_files:
    print("No image files found in the selected folder. Exiting...")
    exit()

print(f"Found {len(image_files)} images. Processing...")

# Define PCD thresholds and distance threshold for PCD center proximity to Plunger center
PCD_MIN = 275
PCD_MAX = 350
PCD_CENTER_THRESHOLD = 20

def calculate_circumcircle(p1, p2, p3):
    A, B, C = np.array(p1), np.array(p2), np.array(p3)
    midAB, midBC = (A + B) / 2, (B + C) / 2
    AB_perp, BC_perp = np.array([- (B - A)[1], (B - A)[0]]), np.array([- (C - B)[1], (C - B)[0]])
    try:
        t = np.linalg.solve(np.vstack([AB_perp, -BC_perp]).T, midBC - midAB)
        circumcenter = midAB + t[0] * AB_perp
    except np.linalg.LinAlgError:
        return None, None
    return circumcenter.astype(int), 2 * np.linalg.norm(circumcenter - A)

for img_file in image_files:
    img_path = os.path.join(folder_selected, img_file)
    results = model(img_path)
    for result in results:
        output_img = result.plot()
        detected_objects = [model.names[int(box.cls)] for box in result.boxes]
        print(f"Detected objects: {detected_objects}")

        petal_centers = [(int((box.xyxy[0][0] + box.xyxy[0][2]) / 2), int((box.xyxy[0][1] + box.xyxy[0][3]) / 2))
                         for box, obj_name in zip(result.boxes, detected_objects) if obj_name == "Petal"]
        petal_count = len(petal_centers)
        exactly_three_petals = petal_count == 3

        plunger_found = "Plunger" in detected_objects
        toenail_found = "Toenail" in detected_objects
        bend_found = "Bend" in detected_objects

        plunger_center = None
        for box, obj_name in zip(result.boxes, detected_objects):
            if obj_name == "Plunger":
                plunger_center = (int((box.xyxy[0][0] + box.xyxy[0][2]) / 2), int((box.xyxy[0][1] + box.xyxy[0][3]) / 2))
                break

        valid_pcd = False
        best_circumcenter, best_diameter = None, None

        if exactly_three_petals:
            print("Exactly 3 petals found. Calculating PCD...")
            best_circumcenter, best_diameter = calculate_circumcircle(*petal_centers)
            if best_circumcenter is not None:
                pcd_distance = np.linalg.norm(np.array(best_circumcenter) - np.array(plunger_center))
                print(f"PCD: {best_diameter:.2f}, Distance from Plunger: {pcd_distance:.2f}")
                valid_pcd = (PCD_MIN <= best_diameter <= PCD_MAX) and (pcd_distance <= PCD_CENTER_THRESHOLD)

        if best_circumcenter is not None:
            cv2.circle(output_img, best_circumcenter, int(best_diameter / 2), (255, 0, 0), 2)
            cv2.putText(output_img, f"PCD: {best_diameter:.2f}, Distance: {pcd_distance:.2f}",
                        (best_circumcenter[0] - 50, best_circumcenter[1] - 10),
                        cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 0, 0), 2)

        cv2.imshow("YOLO Inference", output_img)
        print(f"Processed: {img_file}")

        if not exactly_three_petals or not plunger_found or toenail_found or bend_found or not valid_pcd:
            if not (plunger_found or petal_centers):  # Skip waiting if empty position
                print("No plunger or petals found. Auto-advancing...")
                cv2.waitKey(1)  # Auto-advance
            else:
                print("Condition not met: Waiting for key press...")
                key = cv2.waitKey(0) & 0xFF
                if key == ord('q'):
                    print("Exiting early...")
                    cv2.destroyAllWindows()
                    exit()
        else:
            cv2.waitKey(1)  # Auto-advance if conditions are met

cv2.destroyAllWindows()
print("Inference completed for all images.")

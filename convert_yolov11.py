import torch

# Load the YOLOv11 model checkpoint
checkpoint = torch.load("trained.pt", map_location="cpu")

# Extract the model
model = checkpoint["model"]

# Convert weights to float32
model = model.float()  # Ensures the model runs with float32 inputs

# Set model to evaluation mode
model.eval()

# Define dummy input in float32
dummy_input = torch.randn(1, 3, 640, 640).float()

# Export the model to ONNX format
onnx_model_path = "yolov11.onnx"
torch.onnx.export(
    model,
    dummy_input,
    onnx_model_path,
    opset_version=11,
    input_names=["input"],
    output_names=["output"]
)

print(f"✅ YOLOv11 model successfully converted to ONNX: {onnx_model_path}")

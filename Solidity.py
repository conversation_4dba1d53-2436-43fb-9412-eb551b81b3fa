from pypylon import pylon
import cv2
import warnings
import numpy as np
from collections import deque

warnings.simplefilter('ignore', DeprecationWarning)


def moving_average_filter(values, new_value, max_size=10):
    values.append(new_value)
    if len(values) > max_size:
        values.popleft()
    return sum(values) / len(values)


def calculate_solidity(contour):
    area = cv2.contourArea(contour)
    hull = cv2.convexHull(contour)
    hull_area = cv2.contourArea(hull)
    if hull_area == 0:
        return 0
    return area / hull_area


def calculate_local_solidity(contour, segments=8):
    center, radius = cv2.minEnclosingCircle(contour)
    center = np.array(center, dtype=np.int32)

    local_solidities = []
    angles = np.linspace(0, 2 * np.pi, segments + 1)

    # Create an empty image to match the input image size
    img_shape = (512, 512)  # Adjust based on actual image size
    contour_mask = np.zeros(img_shape, dtype=np.uint8)
    cv2.drawContours(contour_mask, [contour], -1, 255, thickness=cv2.FILLED)  # Full contour mask

    for i in range(segments):
        # Create a blank mask for the current sector
        sector_mask = np.zeros_like(contour_mask)

        # Define sector region
        sector_pts = [center]

        for angle in np.linspace(angles[i], angles[i + 1], 10):
            pt = (int(center[0] + radius * np.cos(angle)), int(center[1] + radius * np.sin(angle)))
            sector_pts.append(pt)

        sector_pts = np.array([sector_pts], dtype=np.int32)
        cv2.fillPoly(sector_mask, [sector_pts], 255)  # Fill only the sector region

        # Apply the sector mask to the contour mask
        segment_mask = cv2.bitwise_and(contour_mask, sector_mask)

        # Extract contours inside the segment
        contours, _ = cv2.findContours(segment_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        if contours:
            best_segment_contour = max(contours, key=cv2.contourArea)
            solidity = calculate_solidity(best_segment_contour)
            local_solidities.append(solidity)
        else:
            local_solidities.append(0)

    return local_solidities



def main():
    camera = pylon.InstantCamera(pylon.TlFactory.GetInstance().CreateFirstDevice())
    camera.StartGrabbing(pylon.GrabStrategy_LatestImageOnly)
    converter = pylon.ImageFormatConverter()
    converter.OutputPixelFormat = pylon.PixelType_BGR8packed
    converter.OutputBitAlignment = pylon.OutputBitAlignment_MsbAligned

    local_solidity_values = deque(maxlen=10)

    while camera.IsGrabbing():
        grab_result = camera.RetrieveResult(5000, pylon.TimeoutHandling_ThrowException)
        key = cv2.waitKey(1) & 0xFF
        if key == 27:
            break

        if grab_result.GrabSucceeded():
            image = converter.Convert(grab_result)
            img = image.GetArray()
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
            _, thresh = cv2.threshold(gray, 50, 255, cv2.THRESH_BINARY_INV)
            contours, _ = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            if contours:
                best_contour = max(contours, key=cv2.contourArea)
                local_solidities = calculate_local_solidity(best_contour)

                smooth_local_solidities = [moving_average_filter(local_solidity_values, s) for s in local_solidities]

                cv2.drawContours(img, [best_contour], -1, (0, 255, 0), 2)
                overlay_text = [f"Local Solidity {i + 1}: {s:.2f}" for i, s in enumerate(smooth_local_solidities)]

                for i, text in enumerate(overlay_text):
                    cv2.putText(img, text, (10, 30 + i * 30), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)

            cv2.imshow('Blob Detection', img)

        grab_result.Release()

    camera.StopGrabbing()
    cv2.destroyAllWindows()


if __name__ == '__main__':
    main()

from pypylon import pylon
import cv2
import warnings
import numpy as np
from collections import deque

warnings.simplefilter('ignore', DeprecationWarning)

# Moving Average Filter
def moving_average_filter(values, new_value, max_size=10):
    values.append(new_value)
    if len(values) > max_size:
        values.popleft()
    return sum(values) / len(values)

# Calculate the ratio of contour area to enclosing circle area
def calculate_enclosing_circle_ratio(contour):
    area = cv2.contourArea(contour)
    (x, y), radius = cv2.minEnclosingCircle(contour)
    enclosing_circle_area = np.pi * (radius ** 2)
    if enclosing_circle_area == 0:
        return 0
    return area / enclosing_circle_area

def main():
    # Define min and max area thresholds
    MIN_CONTOUR_AREA = 10000   # Minimum area threshold
    MAX_CONTOUR_AREA = 100000  # Maximum area threshold

    camera = pylon.InstantCamera(pylon.TlFactory.GetInstance().CreateFirstDevice())
    camera.StartGrabbing(pylon.GrabStrategy_LatestImageOnly)
    converter = pylon.ImageFormatConverter()
    converter.OutputPixelFormat = pylon.PixelType_BGR8packed
    converter.OutputBitAlignment = pylon.OutputBitAlignment_MsbAligned

    enclosing_circle_ratio_values = deque(maxlen=10)

    while camera.IsGrabbing():
        grab_result = camera.RetrieveResult(5000, pylon.TimeoutHandling_ThrowException)
        key = cv2.waitKey(1) & 0xFF
        if key == 27:  # Press 'ESC' to exit
            break

        if grab_result.GrabSucceeded():
            image = converter.Convert(grab_result)
            img = image.GetArray()
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
            _, thresh = cv2.threshold(gray, 50, 255, cv2.THRESH_BINARY_INV)
            contours, _ = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            # Filter contours by size
            valid_contours = [cnt for cnt in contours if MIN_CONTOUR_AREA <= cv2.contourArea(cnt) <= MAX_CONTOUR_AREA]

            if valid_contours:
                best_contour = max(valid_contours, key=cv2.contourArea)
                enclosing_circle_ratio = calculate_enclosing_circle_ratio(best_contour)
                smooth_enclosing_circle_ratio = moving_average_filter(enclosing_circle_ratio_values, enclosing_circle_ratio)

                # Draw the selected contour
                cv2.drawContours(img, [best_contour], -1, (0, 255, 0), 1)
                text = f"Circle Fit: {smooth_enclosing_circle_ratio:.2f}"
                cv2.putText(img, text, (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)

            cv2.imshow('Blob Detection', img)

        grab_result.Release()

    camera.StopGrabbing()
    cv2.destroyAllWindows()

if __name__ == '__main__':
    main()

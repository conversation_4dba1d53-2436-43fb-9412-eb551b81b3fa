import os
import serial
import time
import json
import cv2
import serial.tools.list_ports
from datetime import datetime
from pypylon import pylon
import warnings
import subprocess

warnings.simplefilter('ignore', DeprecationWarning)

SAVE_IMAGES = True  # Set this flag to True to save captured images

SERIAL_PORT = "/dev/cu.usbmodem146401"
BAUD_RATE = 115200
STEP_X = 4.5
STEP_Y = 4.5
NUM_COLUMNS = 24
NUM_ROWS = 16
SPEED = 6000
ACCELERATION = 1000
START_X = 47
START_Y = 148.5
START_Z = 56 #55.3
Z_INCREMENT = 0.25
NUM_Z_IMAGES = 8  # Number of images to capture at decrementing Z positions
Z_STEP = 0.25  # Step size in the negative Z direction

HELICON_FOCUS_PATH = "/Applications/HeliconFocus.app/Contents/MacOS/HeliconFocus"  # Adjust if needed

def send_gcode(serial_port, command):
    try:
        serial_port.write((command + '\n').encode())
        responses = []
        while True:
            response = serial_port.readline().decode().strip()
            if response:
                responses.append(response)
                if "ok" in response:
                    break
        return responses
    except serial.SerialException as e:
        print(f"Error sending '{command}': {e}")
        return []

def parse_status_response(response):
    try:
        return json.loads(response)
    except json.JSONDecodeError:
        print("Error decoding JSON response")
        return None

def wait_for_idle(serial_port):
    try:
        last_status = None
        while True:
            responses = send_gcode(serial_port, "M408 S0")
            if not responses:
                time.sleep(0.2)
                continue
            for response in responses:
                if response.startswith("{") and response.endswith("}"):
                    status_data = parse_status_response(response)
                    if status_data:
                        current_status = status_data.get("status")
                        if current_status == "I":
                            print("Printer is idle.")
                            return
                        elif current_status == "B" and last_status != "B":
                            print("Printer is busy... Waiting.")
                        last_status = current_status
            time.sleep(0.2)
    except serial.SerialException as e:
        print(f"Error querying status: {e}")

def show_resized_image(window_name, frame, max_width=1920, max_height=1080):
    height, width = frame.shape[:2]
    scale = min(max_width / width, max_height / height)
    new_width = int(width * scale)
    new_height = int(height * scale)
    resized_frame = cv2.resize(frame, (new_width, new_height), interpolation=cv2.INTER_AREA)
    cv2.imshow(window_name, resized_frame)

def adjust_z_position(serial_port, camera, converter, z_start, z_increment):
    current_z = z_start
    send_gcode(serial_port, f"G1 Z{current_z:.2f} F2000")
    wait_for_idle(serial_port)

    print("Adjust Z-axis position using W and S keys. Press Enter to confirm.")

    while True:
        grab_result = camera.RetrieveResult(5000, pylon.TimeoutHandling_ThrowException)
        if grab_result.GrabSucceeded():
            image = converter.Convert(grab_result)
            frame = image.GetArray()
            show_resized_image("Live View", frame)
            key = cv2.waitKey(1)

            if key == 27:  # ESC to exit
                print("Exiting Z adjustment mode.")
                break

            elif key == 13:  # Enter key to confirm
                print(f"Z-axis position confirmed at {current_z:.2f}.")
                return current_z

            elif key == ord('w'):
                current_z += z_increment
                print(f"Moving Z up to {current_z:.2f}.")
                send_gcode(serial_port, f"G1 Z{current_z:.2f} F2000")
                wait_for_idle(serial_port)

            elif key == ord('s'):
                current_z -= z_increment
                print(f"Moving Z down to {current_z:.2f}.")
                send_gcode(serial_port, f"G1 Z{current_z:.2f} F2000")
                wait_for_idle(serial_port)

        grab_result.Release()

def focus_stack(folder, output_image, image_files):
    """
    Run Helicon Focus on the captured Z-stack and save only the final stacked image.
    """
    image_list_file = os.path.join(folder, "image_list.txt")

    try:
        with open(image_list_file, 'w') as f:
            f.write("\n".join(image_files))

        cmd = [
            HELICON_FOCUS_PATH, "-silent",
            "-i", image_list_file,
            "-save:" + output_image,
            "-mp:0", "-rp:6", "-sp:7", "-j:100"
        ]

        print(f"Running command: {' '.join(cmd)}")
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print(f"Helicon Focus Output:\n{result.stdout}")

        if os.path.exists(output_image):
            print(f"Focus stacking completed. Output saved as {output_image}")

        # Delete the temporary image list file
        os.remove(image_list_file)

    except subprocess.CalledProcessError as e:
        print(f"Error during focus stacking: {e.stderr}")
    except Exception as e:
        print(f"Unexpected error: {e}")


# Function to capture Z-stack and then process it using focus stacking
def capture_z_stack(serial_port, camera, converter, z_start, num_images, z_step, folder, row_label, column_number):
    """
    Capture a Z-stack at a position, process it with focus stacking,
    save only the final stacked image, and delete the intermediate images.
    """
    image_files = []

    for i in range(num_images):
        z_pos = z_start - (i * z_step)
        send_gcode(serial_port, f"G1 Z{z_pos:.2f} F2000")
        wait_for_idle(serial_port)

        grab_result = camera.RetrieveResult(5000, pylon.TimeoutHandling_ThrowException)

        if grab_result.GrabSucceeded():
            image = converter.Convert(grab_result)
            frame = image.GetArray()
            show_resized_image("Live View", frame)
            cv2.waitKey(1)

            # Save each Z-stack image in the timestamped folder
            image_name = f"{row_label}{column_number}_Z{z_pos:.2f}.jpg"
            image_path = os.path.join(folder, image_name)
            cv2.imwrite(image_path, frame)
            image_files.append(image_path)
            print(f"Saved: {image_path}")

            grab_result.Release()

    # Perform focus stacking and save final image
    final_image_path = os.path.join(folder, f"{row_label}{column_number}.jpg")  # e.g., "A1.jpg"
    focus_stack(folder, final_image_path, image_files)

    # Delete all intermediate images after stacking
    for img in image_files:
        os.remove(img)

    print(f"Deleted intermediate Z-stack images for {row_label}{column_number}")


def zigzag_move(serial_port, camera, converter, step_x, step_y, num_columns, num_rows, speed, acceleration, start_x, start_y, z_adjusted, save_images):
    """
    Move in a zigzag pattern across the grid while capturing and stacking images at each position.
    """
    if save_images:
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        os.makedirs(timestamp, exist_ok=True)

    send_gcode(serial_port, f"M204 P{acceleration} T{acceleration}")  # Set acceleration
    send_gcode(serial_port, f"G1 X{start_x} Y{start_y} F6000")  # Move to the starting position
    wait_for_idle(serial_port)

    current_y = start_y
    current_z = z_adjusted  # Use the manually adjusted Z position
    feedrate = f"F{speed}"

    for row in range(num_rows):
        row_label = chr(65 + row)  # Label rows as 'A', 'B', 'C', etc.

        if row % 2 == 0:
            # Left-to-right movement
            for col in range(num_columns):
                target_x = start_x + (col * step_x)
                send_gcode(serial_port, f"G1 X{target_x:.2f} Y{current_y:.2f} Z{current_z:.2f} {feedrate}")
                wait_for_idle(serial_port)
                time.sleep(0.2)

                # Capture and stack images at this position
                capture_z_stack(serial_port, camera, converter, current_z, NUM_Z_IMAGES, Z_STEP, timestamp, row_label, col + 1)

        else:
            # Right-to-left movement
            for col in reversed(range(num_columns)):
                target_x = start_x + (col * step_x)
                send_gcode(serial_port, f"G1 X{target_x:.2f} Y{current_y:.2f} Z{current_z:.2f} {feedrate}")
                wait_for_idle(serial_port)
                time.sleep(0.2)

                # Capture and stack images at this position
                capture_z_stack(serial_port, camera, converter, current_z, NUM_Z_IMAGES, Z_STEP, timestamp, row_label, col + 1)

        # Move to the next row in the Y direction
        current_y -= step_y
        send_gcode(serial_port, f"G1 Y{current_y:.2f} {feedrate}")
        wait_for_idle(serial_port)




def main():
    try:
        with serial.Serial(SERIAL_PORT, BAUD_RATE, timeout=1) as ser:
            camera = pylon.InstantCamera(pylon.TlFactory.GetInstance().CreateFirstDevice())
            camera.Open()

            camera.ExposureAuto.SetValue("Off")  # Disable automatic exposure
            camera.ExposureTime.SetValue(200000.0)  # Set exposure time (adjust as needed)

            # Set image format
            camera.Width.Value = 2400
            camera.Height.Value = camera.Height.Max

            # Get the maximum width and height
            width = camera.Width.Max

            # Center the image by setting the offset
            camera.OffsetX.Value = (width - 1920) // 2

            converter = pylon.ImageFormatConverter()
            converter.OutputPixelFormat = pylon.PixelType_BGR8packed
            converter.OutputBitAlignment = pylon.OutputBitAlignment_MsbAligned

            camera.StartGrabbing(pylon.GrabStrategy_LatestImageOnly)

            # Home the machine
            send_gcode(ser, "G28")
            wait_for_idle(ser)

            # Move to the start position
            send_gcode(ser, f"G1 X{START_X} Y{START_Y} F6000")
            wait_for_idle(ser)

            # Adjust the Z position before starting
            z_adjusted = adjust_z_position(ser, camera, converter, START_Z, Z_INCREMENT)

            # Perform zigzag movement with Z-stack capture at each position
            zigzag_move(ser, camera, converter, STEP_X, STEP_Y, NUM_COLUMNS, NUM_ROWS, SPEED, ACCELERATION, START_X, START_Y, z_adjusted, SAVE_IMAGES)

            # Home the machine after completion
            send_gcode(ser, "G28")
            wait_for_idle(ser)

            camera.StopGrabbing()
    except serial.SerialException as e:
        print(f"Error opening serial port: {e}")



if __name__ == "__main__":
    main()

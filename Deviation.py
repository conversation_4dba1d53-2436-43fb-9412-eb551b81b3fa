from pypylon import pylon
import cv2
import warnings
import numpy as np
from collections import deque

warnings.simplefilter('ignore', DeprecationWarning)


def moving_average_filter(values, new_value, max_size=10):
    values.append(new_value)
    if len(values) > max_size:
        values.popleft()
    return sum(values) / len(values)


def calculate_roundness(contour):
    area = cv2.contourArea(contour)
    perimeter = cv2.arcLength(contour, True)
    if perimeter == 0:
        return 0
    return (4 * np.pi * area) / (perimeter ** 2)


def main():
    camera = pylon.InstantCamera(pylon.TlFactory.GetInstance().CreateFirstDevice())
    camera.StartGrabbing(pylon.GrabStrategy_LatestImageOnly)
    converter = pylon.ImageFormatConverter()
    converter.OutputPixelFormat = pylon.PixelType_BGR8packed
    converter.OutputBitAlignment = pylon.OutputBitAlignment_MsbAligned

    while camera.IsGrabbing():
        grab_result = camera.RetrieveResult(5000, pylon.TimeoutHandling_ThrowException)
        key = cv2.waitKey(1) & 0xFF
        if key == 27:
            break

        if grab_result.GrabSucceeded():
            image = converter.Convert(grab_result)
            img = image.GetArray()
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
            # Otsu's thresholding
            _, thresh = cv2.threshold(gray, 50, 255, cv2.THRESH_BINARY_INV)
            contours, _ = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            if contours:
                best_contour = max(contours, key=cv2.contourArea)
                (x, y), radius = cv2.minEnclosingCircle(best_contour)
                center = (int(x), int(y))
                radius = int(radius)

                # Draw enclosing circle
                cv2.circle(img, center, radius+2, (0, 255, 0), 2)

                # Create a mask for the enclosing circle
                circle_mask = np.zeros_like(gray)
                cv2.circle(circle_mask, center, radius, (255), thickness=cv2.FILLED)

                # Create a mask for the contour
                contour_mask = np.zeros_like(gray)
                cv2.drawContours(contour_mask, [best_contour], -1, (255), thickness=cv2.FILLED)

                # Find the difference between the contour and the circle
                deviation_mask = cv2.bitwise_xor(circle_mask, contour_mask)
                img[deviation_mask == 255] = [0, 0, 255]  # Fill the deviation in red

            cv2.imshow('Blob Detection', img)
        grab_result.Release()

    camera.StopGrabbing()
    cv2.destroyAllWindows()


if __name__ == '__main__':
    main()
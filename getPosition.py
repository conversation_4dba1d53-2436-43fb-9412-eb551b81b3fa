'''
This script demonstrates how to get the real-time positions of the machine using the M408 S2 command.
'''

import serial
import time
import json

# Serial port and baud rate
SERIAL_PORT = '/dev/cu.usbmodem146101'  # Replace with your serial port
BAUD_RATE = 115200


def get_realtime_positions(poll_interval=0.1):
    try:
        with serial.Serial(SERIAL_PORT, BAUD_RATE, timeout=2) as ser:
            # Allow connection to stabilize
            time.sleep(2)

            while True:
                # Send M408 S2 command to get live data
                ser.write(b'M408 S2\n')
                response = ser.readline().decode('utf-8').strip()
                print(f"Raw response: {response}")  # Debug: Print the raw response

                try:
                    # Parse JSON response
                    data = json.loads(response)

                    # Extract machine positions
                    positions = data['coords']['machine']
                    print(f"X: {positions[0]:.3f}, Y: {positions[1]:.3f}, Z: {positions[2]:.3f}")

                except json.JSONDecodeError:
                    print(f"Invalid JSON: {response}")
                except KeyError as e:
                    print(f"Missing key in response: {e}")

                # Wait for the next poll
                time.sleep(poll_interval)

    except serial.SerialException as e:
        print(f"Serial error: {e}")


if __name__ == "__main__":
    get_realtime_positions()

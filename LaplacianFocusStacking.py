import os
import serial
import time
import json
import cv2
import numpy as np
import serial.tools.list_ports
from datetime import datetime
from pypylon import pylon
import warnings

warnings.simplefilter('ignore', DeprecationWarning)

SAVE_IMAGES = True  # Set this flag to True to save captured images

SERIAL_PORT = "/dev/cu.usbmodem146401"
BAUD_RATE = 115200
STEP_X = 4.5
STEP_Y = 4.5
NUM_COLUMNS = 24
NUM_ROWS = 16
SPEED = 6000
ACCELERATION = 1000
START_X = 46.6
START_Y = 148.1
START_Z = 59
Z_INCREMENT = 0.5
NUM_Z_IMAGES = 20  # Number of images to capture at decrementing Z positions
Z_STEP = 0.5  # Step size in the negative Z direction


def send_gcode(serial_port, command):
    try:
        serial_port.write((command + '\n').encode())
        responses = []
        while True:
            response = serial_port.readline().decode().strip()
            if response:
                responses.append(response)
                if "ok" in response:
                    break
        return responses
    except serial.SerialException as e:
        print(f"Error sending '{command}': {e}")
        return []


def parse_status_response(response):
    try:
        return json.loads(response)
    except json.JSONDecodeError:
        print("Error decoding JSON response")
        return None


def wait_for_idle(serial_port):
    try:
        last_status = None
        while True:
            responses = send_gcode(serial_port, "M408 S0")
            if not responses:
                time.sleep(0.2)
                continue
            for response in responses:
                if response.startswith("{") and response.endswith("}"):
                    status_data = parse_status_response(response)
                    if status_data:
                        current_status = status_data.get("status")
                        if current_status == "I":
                            print("Printer is idle.")
                            return
                        elif current_status == "B" and last_status != "B":
                            print("Printer is busy... Waiting.")
                        last_status = current_status
            time.sleep(0.2)
    except serial.SerialException as e:
        print(f"Error querying status: {e}")


def show_resized_image(window_name, frame, max_width=1920, max_height=1080):
    height, width = frame.shape[:2]
    scale = min(max_width / width, max_height / height)
    new_width = int(width * scale)
    new_height = int(height * scale)
    resized_frame = cv2.resize(frame, (new_width, new_height), interpolation=cv2.INTER_AREA)
    cv2.imshow(window_name, resized_frame)


def adjust_z_position(serial_port, camera, converter, z_start, z_increment):
    current_z = z_start
    send_gcode(serial_port, f"G1 Z{current_z:.2f} F2000")
    wait_for_idle(serial_port)

    print("Adjust Z-axis position using W and S keys. Press Enter to confirm.")

    while True:
        grab_result = camera.RetrieveResult(5000, pylon.TimeoutHandling_ThrowException)
        if grab_result.GrabSucceeded():
            image = converter.Convert(grab_result)
            frame = image.GetArray()
            show_resized_image("Live View", frame)
            key = cv2.waitKey(1)

            if key == 27:  # ESC to exit
                print("Exiting Z adjustment mode.")
                break

            elif key == 13:  # Enter key to confirm
                print(f"Z-axis position confirmed at {current_z:.2f}.")
                return current_z

            elif key == ord('w'):
                current_z += z_increment
                print(f"Moving Z up to {current_z:.2f}.")
                send_gcode(serial_port, f"G1 Z{current_z:.2f} F2000")
                wait_for_idle(serial_port)

            elif key == ord('s'):
                current_z -= z_increment
                print(f"Moving Z down to {current_z:.2f}.")
                send_gcode(serial_port, f"G1 Z{current_z:.2f} F2000")
                wait_for_idle(serial_port)

        grab_result.Release()


def capture_z_stack(serial_port, camera, converter, z_start, num_images, z_step):
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    os.makedirs(timestamp, exist_ok=True)

    for i in range(num_images):
        z_pos = z_start - (i * z_step)
        send_gcode(serial_port, f"G1 Z{z_pos:.2f} F2000")
        wait_for_idle(serial_port)

        grab_result = camera.RetrieveResult(5000, pylon.TimeoutHandling_ThrowException)
        if grab_result.GrabSucceeded():
            image = converter.Convert(grab_result)
            frame = image.GetArray()
            show_resized_image("Live View", frame)
            cv2.waitKey(1)
            image_name = f"Z_{z_pos:.2f}.jpg"
            image_path = os.path.join(timestamp, image_name)
            cv2.imwrite(image_path, frame)
            print(f"Saved: {image_path}")
            grab_result.Release()

def focus_stack_images(image_folder):
    """
    Perform focus stacking on images in the given folder and display the result.
    """
    image_files = sorted([f for f in os.listdir(image_folder) if f.endswith(".jpg")])

    if not image_files:
        print("No images found for focus stacking.")
        return

    images = [cv2.imread(os.path.join(image_folder, f)) for f in image_files]

    # Convert images to grayscale for sharpness detection
    gray_images = [cv2.cvtColor(img, cv2.COLOR_BGR2GRAY) for img in images]

    # Compute the Laplacian variance to detect sharp regions
    laplacian_images = [cv2.Laplacian(img, cv2.CV_64F) for img in gray_images]

    # Stack sharpest pixels by selecting the maximum absolute Laplacian response
    stacked_image = np.zeros_like(images[0], dtype=np.uint8)
    height, width = gray_images[0].shape
    best_focus_index = np.zeros((height, width), dtype=np.int32)

    for i, lap in enumerate(laplacian_images):
        abs_lap = np.abs(lap)
        mask = abs_lap > best_focus_index
        best_focus_index[mask] = i

    for y in range(height):
        for x in range(width):
            stacked_image[y, x] = images[best_focus_index[y, x]][y, x]

    # Save and display result
    result_path = os.path.join(image_folder, "focus_stacked_result.jpg")
    cv2.imwrite(result_path, stacked_image)
    print(f"Focus stacked image saved at: {result_path}")

    cv2.imshow("Focus Stacked Image", stacked_image)
    cv2.waitKey(0)
    cv2.destroyAllWindows()


# Modify main() to call focus_stack_images
def main():
    try:
        with serial.Serial(SERIAL_PORT, BAUD_RATE, timeout=1) as ser:
            camera = pylon.InstantCamera(pylon.TlFactory.GetInstance().CreateFirstDevice())
            camera.Open()

            camera.ExposureAuto.SetValue("Off")
            camera.ExposureTime.SetValue(200000.0)

            camera.Width.Value = camera.Width.Max
            camera.Height.Value = camera.Height.Max

            converter = pylon.ImageFormatConverter()
            converter.OutputPixelFormat = pylon.PixelType_BGR8packed
            converter.OutputBitAlignment = pylon.OutputBitAlignment_MsbAligned

            camera.StartGrabbing(pylon.GrabStrategy_LatestImageOnly)

            send_gcode(ser, "G28")
            wait_for_idle(ser)

            send_gcode(ser, f"G1 X{START_X} Y{START_Y} F6000")
            wait_for_idle(ser)

            z_adjusted = adjust_z_position(ser, camera, converter, START_Z, Z_INCREMENT)

            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            capture_z_stack(ser, camera, converter, z_adjusted, NUM_Z_IMAGES, Z_STEP)

            focus_stack_images(timestamp)  # Perform focus stacking

            send_gcode(ser, "G28")
            wait_for_idle(ser)

            camera.StopGrabbing()
    except serial.SerialException as e:
        print(f"Error opening serial port: {e}")


if __name__ == "__main__":
    main()

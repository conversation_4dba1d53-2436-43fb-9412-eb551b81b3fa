import os
import cv2
import torch
import torch.nn as nn
import torch.optim as optim
import tkinter as tk
from tkinter import filedialog
from torch.utils.data import DataLoader, Dataset
import torchvision.transforms as transforms
from PIL import Image  # Import PIL for conversion

# Select folder for training images
root = tk.Tk()
root.withdraw()
folder_selected = filedialog.askdirectory(title="Select folder of good images")

if not folder_selected:
    print("No folder selected. Exiting...")
    exit()

print(f"Training on images from: {folder_selected}")

# Dataset Loader for 640x640 grayscale images
class ImageDataset(Dataset):
    def __init__(self, folder, img_size=(640, 640)):
        self.folder = folder
        self.img_size = img_size
        self.image_files = [f for f in os.listdir(folder) if f.endswith(('.jpg', '.png', '.jpeg'))]
        self.transform = transforms.Compose([
            transforms.Resize(self.img_size),  # Resize to 640x640
            transforms.Grayscale(num_output_channels=1),  # Convert to grayscale
            transforms.ToTensor(),  # Convert to PyTorch tensor
            transforms.Normalize((0.5,), (0.5,))  # Normalize
        ])

    def __len__(self):
        return len(self.image_files)

    def __getitem__(self, idx):
        img_path = os.path.join(self.folder, self.image_files[idx])
        img = cv2.imread(img_path, cv2.IMREAD_GRAYSCALE)  # Load as grayscale (NumPy array)

        # Convert NumPy image to PIL Image (Fixes the error)
        img = Image.fromarray(img)  # Convert OpenCV NumPy array to PIL Image

        img = self.transform(img)  # Apply transformations
        return img


# Load dataset
dataset = ImageDataset(folder_selected)
train_loader = DataLoader(dataset, batch_size=8, shuffle=True)

# Define Autoencoder Model
class Autoencoder(nn.Module):
    def __init__(self):
        super(Autoencoder, self).__init__()
        self.encoder = nn.Sequential(
            nn.Conv2d(1, 64, kernel_size=3, stride=1, padding=1),
            nn.ReLU(),
            nn.MaxPool2d(2, 2),
            nn.Conv2d(64, 128, kernel_size=3, stride=1, padding=1),
            nn.ReLU(),
            nn.MaxPool2d(2, 2),
            nn.Conv2d(128, 256, kernel_size=3, stride=1, padding=1),
            nn.ReLU(),
            nn.MaxPool2d(2, 2),
            nn.Conv2d(256, 512, kernel_size=3, stride=1, padding=1),
            nn.ReLU(),
            nn.MaxPool2d(2, 2)
        )
        self.decoder = nn.Sequential(
            nn.Conv2d(512, 256, kernel_size=3, stride=1, padding=1),
            nn.ReLU(),
            nn.Upsample(scale_factor=2),
            nn.Conv2d(256, 128, kernel_size=3, stride=1, padding=1),
            nn.ReLU(),
            nn.Upsample(scale_factor=2),
            nn.Conv2d(128, 64, kernel_size=3, stride=1, padding=1),
            nn.ReLU(),
            nn.Upsample(scale_factor=2),
            nn.Conv2d(64, 1, kernel_size=3, stride=1, padding=1),
            nn.Sigmoid(),
            nn.Upsample(scale_factor=2)
        )

    def forward(self, x):
        x = self.encoder(x)
        x = self.decoder(x)
        return x

# Train the model
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
model = Autoencoder().to(device)
criterion = nn.MSELoss()
optimizer = optim.Adam(model.parameters(), lr=0.001)

# Training loop
num_epochs = 20
for epoch in range(num_epochs):
    model.train()
    total_loss = 0
    for images in train_loader:
        images = images.to(device)
        outputs = model(images)
        loss = criterion(outputs, images)
        optimizer.zero_grad()
        loss.backward()
        optimizer.step()
        total_loss += loss.item()
    print(f"Epoch [{epoch+1}/{num_epochs}], Loss: {total_loss/len(train_loader):.4f}")

# Save trained model
torch.save(model.state_dict(), "autoencoder_640x640.pth")
print("Training complete! Model saved as autoencoder_640x640.pth")

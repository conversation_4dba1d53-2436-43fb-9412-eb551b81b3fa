import os
import cv2
import numpy as np
import tkinter as tk
from tkinter import filedialog


def select_folder(title):
    root = tk.Tk()
    root.withdraw()
    folder_selected = filedialog.askdirectory(title=title)
    return folder_selected


def combine_images(folder1, folder2, output_folder):
    if not os.path.exists(output_folder):
        os.makedirs(output_folder)

    images1 = set(os.listdir(folder1))
    images2 = set(os.listdir(folder2))

    common_images = images1.intersection(images2)

    for image_name in common_images:
        img1_path = os.path.join(folder1, image_name)
        img2_path = os.path.join(folder2, image_name)

        img1 = cv2.imread(img1_path)
        img2 = cv2.imread(img2_path)

        if img1 is None or img2 is None:
            print(f"Skipping {image_name}: Could not read one or both images.")
            continue

        # Resize images to the same height for consistency
        height = min(img1.shape[0], img2.shape[0])
        img1 = cv2.resize(img1, (int(img1.shape[1] * (height / img1.shape[0])), height))
        img2 = cv2.resize(img2, (int(img2.shape[1] * (height / img2.shape[0])), height))

        combined = np.hstack((img1, img2))

        output_path = os.path.join(output_folder, image_name)
        cv2.imwrite(output_path, combined)
        print(f"Saved: {output_path}")


if __name__ == "__main__":
    print("Select the first folder")
    folder1 = select_folder("Select the first folder")
    print("Select the second folder")
    folder2 = select_folder("Select the second folder")
    print("Select output folder")
    output_folder = select_folder("Select output folder")

    combine_images(folder1, folder2, output_folder)
    print("Processing complete.")
